package com.datalink.datamanage.service;

import java.util.List;

import com.datalink.common.core.domain.AjaxResult;
import com.datalink.datamanage.domain.TblAsn;
import com.datalink.datamanage.domain.TblAsnArticle;
import com.datalink.datamanage.domain.TblAsnItem;
import com.datalink.datamanage.domain.TblOrderAsnQuantity;

/**
 * ASNService接口
 *
 * <AUTHOR>
 * @date 2021-08-04
 */
public interface ITblAsnService
{
    /**
     * 查询ASN
     *
     * @param asnId ASNID
     * @return ASN
     */
    public TblAsn selectTblAsnById(Long asnId);

    /**
     *
     * 查询ASN
     *
     * @param asnCode ASNCode
     * @return ASN
     */
    public TblAsn selectTblAsnByAsnCode(String asnCode);

    /**
     * 查询ASN列表
     *
     * @param tblAsn ASN
     * @return ASN集合
     */
    public List<TblAsn> selectTblAsnList(TblAsn tblAsn);

    /**
     * 查询ASN及行项目列表
     *
     * @param tblAsn ASN
     * @return ASN集合
     */
    public List<TblAsn> selectTblAsnWithItemList(TblAsn tblAsn);

    /**
     * 新增ASN
     *
     * @param tblAsn ASN
     * @return 结果
     */
    public int insertTblAsn(TblAsn tblAsn);

    /**
     * 修改ASN
     *
     * @param tblAsn ASN
     * @return 结果
     */
    public int updateTblAsn(TblAsn tblAsn);

    /**
     * 修改ASN(不包含行项目)
     *
     * @param tblAsn ASN
     * @return 结果
     */
    public int updateTblAsnOnly(TblAsn tblAsn);

    /**
     * 批量删除ASN
     *
     * @param asnIds 需要删除的ASNID
     * @return 结果
     */
    public int deleteTblAsnByIds(Long[] asnIds);

    /**
     * 删除ASN信息
     *
     * @param asnId ASNID
     * @return 结果
     */
    public int deleteTblAsnById(Long asnId);

    /**
     * 查询ASN列表(接口专用)
     *
     * @param tblAsn ASN
     * @return ASN集合
     */
    public List<TblAsn> selectTblAsnFullList(TblAsn tblAsn);

    /**
     * 查询最大ID
     *
     * @return 最大ID
     */
    public Long selectLastId();

    /**
     * 查询ASN(不包含行项目)
     *
     * @param asnId ASNID
     * @return ASN
     */
    public TblAsn selectTblAsnOnlyById(Long asnId);

    /**
     * 查询ASN行项目列表
     *
     * @param asnId ASNID
     * @return ASN行项目集合
     */
    public List<TblAsnItem> selectTblAsnItemByAsnId(Long asnId);

    /**
     * 查询ASN行项目
     *
     * @param itemId ItemID
     * @return ASN行项目
     */
    public TblAsnItem selectTblAsnItemByItemId(Long itemId);

    /**
     * 查询ASN物料列表
     *
     * @param itemId ItemID
     * @return ASN物料列表
     */
    public List<TblAsnArticle> selectTblAsnArticleByItemId(Long itemId);

    /**
     * 查询ASN行项目列表
     *
     * @param tblAsnItem TblAsnItem
     * @return ASN行项目集合
     */
    public List<TblAsnItem> selectTblAsnItemList(TblAsnItem tblAsnItem);

    /**
     * 查询ASN物料列表
     *
     * @param tblAsnArticle TblAsnArticle
     * @return ASN物料列表
     */
    public List<TblAsnArticle> selectTblAsnArticleList(TblAsnArticle tblAsnArticle);

    /**
     * 查询订单物料剩余
     *
     * @param orderAsnQuantity TblOrderAsnQuantity
     * @return 订单物料剩余集合
     */
    public List<TblOrderAsnQuantity> selectTblOrderAsnQuantityList(TblOrderAsnQuantity orderAsnQuantity);

    AjaxResult printNpsSls(Long asnId, String tz);

    AjaxResult printProdTag(Long asnId, String tz);

    AjaxResult printPickingList(long asnId, String tz);

    /**
     * 查询ASN详细信息用于打印（包含供应商名称和物料名称）
     *
     * @param asnId ASNID
     * @return ASN
     */
    public TblAsn selectTblAsnForPrint(Long asnId);
}
