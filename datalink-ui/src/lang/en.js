export default {
  acceptanceDetailOverview: {
    form: {
      suppCode: 'Supplier Code',
      dateRange: 'Date Range'
    },
    placeholder: {
      suppCode: 'Please enter supplier code',
      startDate: 'Start Date',
      endDate: 'End Date'
    },
    table: {
      settlementNo: 'Settlement No',
      suppCode: 'Supplier Code',
      suppName: 'Supplier Name',
      companyCode: 'Company Code',
      operateTime: 'Operation Time',
      operateType: 'Operation Type',
      operator: 'Operator'
    }
  },
  forecastOverview: {
    form: {
      suppCode: 'Supplier Code',
      dateRange: 'Date Range'
    },
    placeholder: {
      suppCode: 'Please enter supplier code',
      startDate: 'Start Date',
      endDate: 'End Date'
    },
    table: {
      sendTime: 'Send Time',
      suppCode: 'Supplier Code',
      suppName: 'Supplier Name',
      plant: 'Plant',
      operateTime: 'Operation Time',
      operateType: 'Operation Type',
      operator: 'Operator'
    }
  },
  orderOverview: {
    form: {
      suppCode: 'Supplier Code',
      plant: 'Plant',
      dateRange: 'Date Range'
    },
    placeholder: {
      suppCode: 'Please enter supplier code',
      plant: 'Please enter plant',
      startDate: 'Start Date',
      endDate: 'End Date'
    },
    table: {
      suppCode: 'Supplier Code',
      suppName: 'Supplier Name',
      orderNo: 'Order No',
      plant: 'Plant',
      operateTime: 'Operation Time',
      operateType: 'Operation Type',
      operator: 'Operator'
    }
  },
  palletMaintain: {
    form: {
      plant: 'Plant',
      partNo: 'Part No'
    },
    placeholder: {
      plant: 'Please enter plant',
      partNo: 'Please enter part no'
    },
    button: {
      search: 'Search',
      reset: 'Reset',
      edit: 'Edit',
      save: 'Save',
      cancel: 'Cancel'
    },
    table: {
      index: 'No',
      action: 'Action',
      plant: 'Plant',
      partNo: 'Part No',
      partDesc: 'Part Description',
      qtyPerPack: 'Qty Per Pack',
      palletSnpQty: 'Pallet SNP Qty',
      palletLength: 'Pallet Length',
      palletWidth: 'Pallet Width',
      palletHeight: 'Pallet Height',
      containerType: 'Container Type'
    },
    alert: {
      saveBeforeEdit: 'Please save or cancel the current edit row first',
      saveSuccess: 'Save successful'
    }
  },
  internalForecastDownloadNew: {
    form: {
      suppCode: 'Supplier Code',
      createTime: 'Create Time',
      status: 'Status'
    },
    placeholder: {
      suppCode: 'Please enter supplier code',
      startTime: 'Start Time',
      status: 'Please select the status',
      endTime: 'End Time'
    },
    button: {
      search: 'Search',
      reset: 'Reset',
      printSelectedRows: 'Print Selected Rows',
      confirmSelectedRows: 'Confirm Selected Rows'
    },
    table: {
      index: 'No',
      createTime: 'Create Time',
      suppCode: 'Supplier Code',
      plantCode: 'Plant',
      status: 'Status',
      downloadingStatus: 'Downloading Status',
      lastDownloadingTime: 'Last Downloading Time'
    },
    status: {
      notDownloaded: 'Not Downloaded',
      downloaded: 'Downloaded',
      normal: 'Normal',
      abnormal: 'Abnormal'
    },
    alert: {
      selectOrder: 'Please select a row first',
      printSuccess: 'Print succeeded',
      confirmSuccess: 'Confirmed successfully'
    },
    common: {
      loading: 'Loading...'
    }
  },
  deliveryNoteGenerate: {
    form: {
      orderNo: 'Order No',
      suppCode: 'Supplier Code',
      shipStatus: 'Shipping Status',
      dateRange: 'Date Range'
    },
    placeholder: {
      orderNo: 'Please enter order number',
      suppCode: 'Please enter supplier code',
      shipStatus: 'Please select shipping status',
      startTime: 'Start Date',
      endTime: 'End Date'
    },
    button: {
      generate: 'Generate Delivery/Acceptance Note',
      print: 'Print Delivery/Acceptance Note',
      edit: 'Edit',
      save: 'Save',
      cancel: 'Cancel'
    },
    table: {
      index: 'No',
      action: 'Action',
      orderNo: 'Order No',
      lineNo: 'Line No',
      releaseNo: 'Release No',
      partNo: 'Part No',
      partDesc: 'Part Description',
      orderQty: 'Order Qty',
      deliveryQty: 'Delivery Qty',
      totalDeliveryQty: 'Total Delivery Qty',
      qtyPerPack: 'Qty per Pack',
      deliveryDate: 'Delivery Date',
      plant: 'Plant',
      unit: 'Unit'
    },
    alert: {
      selectRow: 'Please select a row first',
      generateSuccess: 'Generated successfully',
      printSuccess: 'Printed successfully',
      saveBeforeEdit: 'Please save the current editing row first',
      saveSuccess: 'Saved successfully'
    }
  },
  acceptanceDownload: {
    form: {
      suppCode: 'Supplier Code',
      status: 'Status',
      dateRange: 'Date Range',
    },
    placeholder: {
      suppCode: 'Please enter supplier code',
      status: 'Please select status',
      startTime: 'Start Time',
      endTime: 'End Time',
    },
    button: {
      downloadSelectedRows: 'Download Selected Rows',
      confirmSelectedRows: 'Confirm Selected Rows',
      edit: 'Edit',
      save: 'Save',
      cancel: 'Cancel'
    },
    table: {
      index: 'NO',
      action: 'Action',
      settlementCode: 'Settlement Code',
      suppName: 'Supplier',
      compCode: 'Company Code',
      status: 'Status',
      invoiceTotalTax: 'Invoice Total Tax',
      invoiceNo: 'Invoice Number',
      invoiceDate: 'Invoice Date',
      invoiceAmount: 'Invoice Amount',
      currency: 'Currency',
      receiveDate: 'Receive Date'
    },
    alert: {
      selectRow: 'Please select a row first',
      downloadSuccess: 'Download succeeded',
      confirmSuccess: 'Confirm succeeded',
      saveBeforeEdit: 'Please save the current editing row first',
      saveSuccess: 'Save succeeded'
    }
  },
  ticketPrint: {
    form: {
      orderNo: 'Order No',
      suppCode: 'Supplier Code',
      shipStatus: 'Shipping Status',
      dateRange: 'Date Range'
    },
    placeholder: {
      orderNo: 'Please enter order number',
      suppCode: 'Please enter supplier code',
      shipStatus: 'Please select shipping status',
      startTime: 'Start Date',
      endTime: 'End Date'
    },
    button: {
      printDeliveryAcceptance: 'Print Delivery/Acceptance Note',
      printPickList: 'Print Pick List',
      printItemTag: 'Print Item Tag',
      printPalletTag: 'Print Pallet Tag'
    },
    table: {
      index: 'No',
      asnNo: 'ASN No',
      orderNo: 'Order No',
      lineNo: 'Line No',
      releaseNo: 'Release No',
      partNo: 'Part No',
      partDesc: 'Part Description',
      plant: 'Plant',
      shipDate: 'Ship Date',
      orderQty: 'Order Qty',
      deliveryQty: 'Delivery Qty',
      qtyPerPack: 'Qty per Pack',
      unit: 'Unit',
      shipStatus: 'Shipping Status',
      deliveryDate: 'Delivery Date',
      warehouse: 'Warehouse',
      palletCount: 'Pallet Count',
      containerType: 'Container Type',
      totalDeliveryQty: 'Total Delivery Qty'
    },
    alert: {
      selectRow: 'Please select a row first',
      printSuccess: 'Print successful'
    },
    dialog: {
      itemTagTitle: 'Print Item Tag',
      lineNo: 'Line No',
      orderNo: 'Order No',
      batchNo: 'Batch No',
      addBatchNo: 'Add Batch',
      removeBatchNo: 'Remove Batch',
      printItemTag: 'Print Item Tag',
      reprintItemTag: 'Reprint Item Tag',
      selectReprintRow: 'Please select the row to reprint',
      reprintSuccess: 'Reprint Item Tag successful',
      inputLineOrder: 'Please enter line no and order no',
      noBatchToDelete: 'No batch to delete',
      table: {
        index: 'No',
        orderNo: 'Order No',
        lineNo: 'Line No',
        releaseNo: 'Release No',
        partNo: 'Part No',
        partDesc: 'Part Description',
        batchNo: 'Batch',
        snp: 'SNP',
        deliveryQty: 'Delivery Qty',
        deliveryDate: 'Delivery Date'
      }
    }
  },
  orderDetail: {
    form: {
      orderNo: 'Order No',
      partNo: 'Part No',
      shipmentStatus: 'Shipment Status',
      orderDate: 'Order Date',
      startDate: 'Start Date',
      endDate: 'End Date',
    },
    placeholder: {
      orderNo: 'Please enter order number',
      partNo: 'Please enter part number',
      shipmentStatus: 'Please select shipment status',
      startDate: 'Start Date',
      endDate: 'End Date',
    },
    button: {
      search: 'Search',
      reset: 'Reset',
    },
    table: {
      orderNo: 'Order No',
      lineNo: 'Line No',
      releaseNo: 'Release No',
      orderStatus: 'Order Status',
      shipmentStatus: 'Shipment Status',
      partNo: 'Part No',
      partDesc: 'Part Description',
      deliveryDate: 'Delivery Date',
      orderQty: 'Order Qty',
      unit: 'Unit',
    },
    shipmentStatusOptions: {
      not_shipped: 'Not Shipped',
      shipping: 'Shipping',
      delivered: 'Delivered',
    }
  },

  web: {
    title: 'DataLink System'
  },
  loadingMessage: 'Loading system resources, please wait patiently',
  dashboard: {
    title: '7-Day Data Summary',
    stats: {
      Order: 'Orders',
      Forecast: 'Forecast',
      Consignment: 'Consignment Inventory',
      Inventory: 'Inventory',
      Feedback: 'Receiving Feedback'
    },
    orderAnalysis: '7-Day Data Summary',
    totalOrders: 'Total Orders',
    completedOrders: 'Completed Orders',
    pendingOrders: 'Pending Orders',
    compareToYesterday: 'Compared to Yesterday',
    chart: {
      totalOrders: 'Total Orders',
      completedOrders: 'Completed',
      pendingOrders: 'Pending'
    }
  },
  route: {
    dashboard: 'Dashboard',
    orderDownload: 'Order Download',
    dataManagement: 'Data Management',
    dataReceive: 'Data Receiving',
    order: 'Order',
    forecast: 'Forecast',
    consignmentInventory: 'Consignment Inventory',
    inventory: 'Inventory',
    receiptFeedback: 'Receiving Feedback',
    ASN: 'ASN',
    dataSend: 'Data Sending',
    systemManagement: 'System Management',
    userManagement: 'User Management',
    roleManagement: 'Role Management',
    menuManagement: 'Menu Management',
    supplierManagement: 'Supplier Management',
    carrierManagement: 'Carrier Management',
    dictionaryManagement: 'Dictionary Management',
    parameterSettings: 'Parameter Settings',
    notice: 'Notice',
    logManagement: 'Log Management',
    operationLog: 'Operation Log',
    loginLog: 'Login Log',
    systemMonitoring: 'System Monitoring',
    onlineUsers: 'Online Users',
    scheduledTasks: 'Scheduled Tasks',
    dataMonitoring: 'Data Monitoring',
    serverMonitoring: 'Server Monitoring',
    cacheMonitoring: 'Cache Monitoring',
    systemTools: 'System Tools',
    formBuilder: 'Form Builder',
    codeGeneration: 'Code Generation',
    systemInterface: 'System Interface',
    profile: 'Profile',
    dictData: 'Dictionary Data',
    dispatchLog: 'Dispatch Log',
    modifyGeneratedConfig: 'Modify Generated Config',
    orderDetail: 'Order Details',
    purchaseOrderDetail: 'Purchase Order Details',
    acceptanceDownload: 'Acceptance Details Download',
    deliveryNoteGenerate: 'Generate Delivery/Acceptance Note',
    ticketPrint: 'Ticket Print',
    palletMaintain: 'Pallet Maintain',
    orderOverview: 'Order Overview',
    forecastOverview: 'Forecast Overview',
    acceptanceDetailOverview: 'Acceptance Detail Overview',
    forecastDetail: 'Forecast Details',
    consignmentInventoryDetail: 'Consignment Inventory Details',
    inventoryDetail: 'Inventory Details',
    receiptFeedbackDetail: 'Receiving Feedback Details',
    ASNDetail: 'ASN Details',
    ASNEdit: 'ASN Edit',
    internalForecast: 'Internal Forecast',
    loadProposalVehicleRegistration: 'Load Proposal Vehicle Registration',
    loadProposalVehicleConfirmation: 'Load Proposal Vehicle Confirmation',
    supplyPlan: 'Supply Plan',
    kanban: 'Single Function Integration',
    ePartner: 'e-Partner',
    kanbaA8245: 'Integration Data (Production Line)',
    kanbaM4028: 'Integration Data (SV)',
    kanbaN2396: 'Integration Data (KD)',
    downloadFunction: 'Download Function',
    printFunction: 'Print Function',
    overview: 'Overview'
  },
  order: {
    form: {
      orderCode: 'Order Code',
      compCode: 'Company Code',
      compName: 'Company Name',
      suppCode: 'Supplier Code',
      suppName: 'Supplier Name',
      plantCode: 'Plant Code',
      plantName: 'Plant Name',
      plannerNo: 'Planner Group',
      plannerName: 'Planner Group Description',
      createTime: 'Creation Time',
      sapUpdateTime: 'SAP Update Time',
      receiveTime: 'Receive Time',
      isRead: 'Is Read',
      isComplete: 'Is Complete',
      timeBegin: 'Time Window Start',
      timeEnd: 'Time Window End',
      itemNo: 'Line Number',
      purDocType: 'Purchase Document Type',
      itemType: 'Item Category',
      text: 'Purchase Order Header',
      delIden: 'Delete Indicator',
      shortText: 'Purchase Material Description',
      oldArticleNo: 'Old Material Number',
      articleNo: 'Material Number',
      articleName: 'Material Name',
      deliveryDate: 'Delivery Date',
      quantity: 'Quantity',
      unit: 'Unit',
      workbinNo: 'Bin Type',
      workbinName: 'Bin Type Description',
      qtyPerPack: 'Standard Packaging Quantity',
      unloadingNo: 'Unload Loc No.',
      unloadingName: 'Unload Dest.',
      state: 'Status',
      netPrice: 'Net Price',
      priceUnit: 'Price Unit',
      orderNetWorth: 'Order Net Worth',
      currencyCode: 'Currency',
      stockLoc: 'Stock Location',
      locDes: 'Stock Location Description',
      locAdd: 'Stock Location Address',
      rcvName: 'Receiver Name',
      rcvTel: 'Receiver Phone',
      inspeStrategy: 'Inspection Strategy',
      zipCode: 'Postal Code',
      city: 'City',
      countryCode: 'Country Code',
      addTimeZone: 'Address Time Zone',
      street2: 'Street 2',
      street3: 'Street 3',
      street4: 'Street 4',
      depot: 'Depot'
    },
    placeholder: {
      orderCode: 'Please enter order code',
      compCode: 'Please enter company code',
      compName: 'Please enter company name',
      suppCode: 'Please enter supplier code',
      suppName: 'Please enter supplier name',
      plantCode: 'Please enter plant code',
      plantName: 'Please enter plant name',
      plannerNo: 'Please enter planner group',
      plannerName: 'Please enter planner group description',
      startTime: 'Start time',
      endTime: 'End time',
      select: 'Please select',
      itemNo: 'Please enter line number',
      purDocType: 'Please enter purchase document type',
      itemType: 'Please enter item category',
      text: 'Please enter purchase order header',
      delIden: 'Please enter delete indicator',
      shortText: 'Please enter purchase material description',
      oldArticleNo: 'Please enter old material number',
      articleNo: 'Please enter material number',
      articleName: 'Please enter material name',
      deliveryDate: 'Please enter delivery date',
      quantity: 'Please enter quantity',
      unit: 'Please enter unit',
      workbinNo: 'Please enter bin type',
      workbinName: 'Please enter bin type description',
      qtyPerPack: 'Please enter standard packaging quantity',
      unloadingNo: 'Please enter Unload Loc No.',
      unloadingName: 'Please enter Unload Dest.',
      state: 'Please enter status',
      netPrice: 'Please enter net price',
      priceUnit: 'Please enter price unit',
      orderNetWorth: 'Please enter order net worth',
      currencyCode: 'Please enter currency',
      stockLoc: 'Please enter stock location',
      locDes: 'Please enter stock location description',
      locAdd: 'Please enter stock location address',
      rcvName: 'Please enter receiver name',
      rcvTel: 'Please enter receiver phone',
      inspeStrategy: 'Please enter inspection strategy',
      zipCode: 'Please enter postal code',
      city: 'Please enter city',
      countryCode: 'Please enter country code',
      addTimeZone: 'Please enter address time zone',
      street2: 'Please enter street 2',
      street3: 'Please enter street 3',
      street4: 'Please enter street 4',
      depot: 'Please enter the depot'
    },
    table: {
      orderCode: 'Order Code',
      compCode: 'Company Code',
      compName: 'Company Name',
      suppCode: 'Supplier Code',
      suppName: 'Supplier Name',
      downloadStatus: 'Download Status',
      plantCode: 'Plant Code',
      plantName: 'Plant Name',
      unloadingNo: 'Unload Loc No.',
      unloadingName: 'Unload Dest.',
      plannerNo: 'Planner Group',
      plannerName: 'Planner Group Description',
      customerCode: 'Customer Code',
      requester: 'Requester',
      createTime: 'Creation Time',
      sapUpdateTime: 'Update Time',
      receiveTime: 'Receive Time',
      isRead: 'Is Read',
      isComplete: 'Is Complete',
      timeBegin: 'Time Window Start',
      timeEnd: 'Time Window End',
      index: 'Index',
      actions: 'Actions'
    },
    button: {
      search: 'Search',
      reset: 'Reset',
      createASN: 'Create ASN',
      orderConfirm: 'Order Confirm',
      printItemTag: 'Print Item Tag',
      printTxt: 'Download TXT',
      orderDownload: 'Order Download',
      printPartsInstructionNote: 'Print Parts Instruction Note',
      export: 'Export',
      add: 'Add',
      delete: 'Delete',
      confirm: 'Confirm',
      cancel: 'Cancel',
      notification: 'Notification',
      warning: 'Warning'
    },
    divider: {
      orderItems: 'Order Line Item Information'
    },

    title: {
      add: 'Add Order',
      update: 'Update Order'
    },
    success: {
      add: 'Successfully Added',
      update: 'Successfully Updated',
      delete: 'Successfully Deleted',
      confirm: 'Successfully Confirm'
    },
    confirm: {
      delete:
        'Are you sure you want to delete the data item with order code "{orderIds}"?',
      confirmAllItems:
        'Are you sure you want to update the whole order even if not all items are selected?',
      downloadAllItems:
        'Are you sure you want to download the whole order even if not all items are selected?'
    },
    alert: {
      deleteItem:
        'Please select the order line item data you want to delete first',
      selectOrder: 'Please select at least one order',
      onlyNewCanConfirm: 'Order {notNewOrderCodes} status is not ‘New’, cannot confirm.',
      sameClient: 'The selected orders must be for the same client',
      sameSupplier: 'The selected orders must be from the same supplier',
      samePlant: 'The selected orders must be for the same plant',
      sameDepot: 'The selected orders must have the same depot',
      sameUnloading: 'The selected orders must have the same unloading location',
      orderCompleted: 'Cannot create ASN for completed orders',
      znbsNotAllowed: 'Operation Forbidden! This purchase order is for drop shipment business.'
    },
    rules: {
      compCode: 'Company code cannot be empty',
      compName: 'Company name cannot be empty',
      plantCode: 'Plant code cannot be empty',
      plantName: 'Plant name cannot be empty',
      suppCode: 'Supplier code cannot be empty',
      suppName: 'Supplier name cannot be empty',
      plannerNo: 'Planner group cannot be empty',
      plannerName: 'Planner group description cannot be empty',
      orderCode: 'Order code cannot be empty'
    },
    orderDetail: {
      baseInfoTitle: 'Order Information',
      itemsInfoTitle: 'Line Item Information',
      orderCode: 'Order Code',
      compCode: 'Company Code',
      compName: 'Company Name',
      plantCode: 'Plant Code',
      plantName: 'Plant Name',
      plannerNo: 'Planner Group',
      plannerName: 'Planner Group Description',
      suppCode: 'Supplier Code',
      suppName: 'Supplier Name',
      itemNo: 'Line Number',
      status: 'Order Status',
      releaseNo: 'Release Number',
      articleNo: 'Material Number',
      articleName: 'Material Name',
      quantity: 'Quantity',
      unit: 'Unit',
      netPrice: 'Purchase Price',
      priceUnit: 'Price Unit',
      currencyCode: 'Currency',
      deliveryDate: 'Delivery Date',
      qtyPerPack: 'Standard Packaging Quantity',
      deliverySplit: 'Split Count',
      workbinNo: 'Bin Type',
      workbinName: 'Bin Type Description',
      state: 'Status',
      purDocType: 'Purchase Document Type',
      itemType: 'Item Category',
      text: 'Purchase Order Header',
      orderNetWorth: 'Order Net Worth in Purchase Order Currency',
      delIden: 'Delete Indicator in Purchase Document',
      shortText: 'Purchase Material Description',
      oldArticleNo: 'Old Material Number',
      unloadingNo: 'Unload Loc No.',
      unloadingName: 'Unload Dest.',
      stockLoc: 'Stock Location',
      locDes: 'Stock Location Description',
      locAdd: 'Stock Location Address',
      rcvName: 'Receiver Name',
      rcvTel: 'Receiver Phone',
      inspeStrategy: 'Inspection Strategy',
      zipCode: 'Postal Code',
      city: 'City',
      countryCode: 'Country Code',
      addTimeZone: 'Address Time Zone',
      street2: 'Street 2',
      street3: 'Street 3',
      street4: 'Street 4',
      title: 'Title',
      content: 'Content',
      customerOrderCode: 'Customer Order Number',
      customerOrderLineCode: 'Customer Order Line Number',
      customerDeliveryDate: 'Customer Delivery Date',
      productType: 'Production Method',
      rcvType: 'Receiving Method',
      purchaseType: 'Self-Procurement Type',
      depot: 'Depot'
    }
  },
  purchaseOrder: {
    orderDetail: {
      baseInfoTitle: 'Order Information',
      partsInfoTitle: 'Part Information',
      orderCode: 'Order Code',
      lineNo: 'Line No',
      releaseNo: 'Release No',
      suppAddr: 'Supplier Address',
      rcvAddr: 'Receiving Address',
      depot: 'Warehouse',
      plantName: 'Plant',
      shipStatus: 'Shipping Status',
      deliveryDate: 'Delivery Date',
      partNo: 'Part No',
      partDesc: 'Part Description',
      quantity: 'Quantity',
      orderStatus: 'Order Status',
      unit: 'Unit',
      qtyPerPack: 'Standard Pack Qty',
      buyerCode: 'Buyer Code',
      title: 'Title',
      content: 'Content'
    },
    form: {
      orderNo: 'Order No',
      orderNoPlaceholder: 'Please enter order number',
      partNo: 'Part No',
      partNoPlaceholder: 'Please enter part number',
      shipStatus: 'Shipping Status',
      shipStatusPlaceholder: 'Please select shipping status',
      dateRange: 'Date Range',
      startDate: 'Start Date',
      endDate: 'End Date',
      to: 'to'
    },
    placeholder: {
      orderNo: 'Please enter order number',
      partNo: 'Please enter part number',
      shipStatus: 'Please select shipping status',
      dateRange: 'Please select date range',
      to: 'to',
      startDate: 'Start Date',
      endDate: 'End Date'
    },
    table: {
      orderNo: 'Order No',
      lineNo: 'Line No',
      releaseNo: 'Release No',
      orderStatus: 'Order Status',
      shipStatus: 'Shipping Status',
      partNo: 'Part No',
      partDesc: 'Part Description',
      deliveryDate: 'Delivery Date',
      orderQty: 'Order Quantity',
      unit: 'Unit'
    },
    search: 'Search',
    button: {
      search: 'Search',
      reset: 'Reset',
      createASN: 'Create ASN',
      printItemTag: 'Print Item Tag',
      printTxt: 'Download TXT',
      printPartsInstructionNote: 'Print Parts Instruction Note',
      export: 'Export',
      add: 'Add',
      delete: 'Delete',
      confirm: 'Confirm',
      cancel: 'Cancel',
      notification: 'Notification',
      warning: 'Warning'
    },
    divider: {
      orderItems: 'Order Line Item Information'
    },

    title: {
      add: 'Add Order',
      update: 'Update Order'
    },
    success: {
      add: 'Successfully Added',
      update: 'Successfully Updated',
      delete: 'Successfully Deleted'
    },
    confirm: {
      delete:
        'Are you sure you want to delete the data item with order code "{orderIds}"?',
    },
    alert: {
      deleteItem:
        'Please select the order line item data you want to delete first',
      selectOrder: 'Please select at least one order',
      sameClient: 'The selected orders must be for the same client',
      sameSupplier: 'The selected orders must be from the same supplier',
      samePlant: 'The selected orders must be for the same plant',
      sameDepot: 'The selected orders must have the same depot',
      sameUnloading: 'The selected orders must have the same unloading location',
      orderCompleted: 'Cannot create ASN for completed orders',
      znbsNotAllowed: 'Operation Forbidden! This purchase order is for drop shipment business.'
    },
    rules: {
      compCode: 'Company code cannot be empty',
      compName: 'Company name cannot be empty',
      plantCode: 'Plant code cannot be empty',
      plantName: 'Plant name cannot be empty',
      suppCode: 'Supplier code cannot be empty',
      suppName: 'Supplier name cannot be empty',
      plannerNo: 'Planner group cannot be empty',
      plannerName: 'Planner group description cannot be empty',
      orderCode: 'Order code cannot be empty'
    }
  },
  orderDownload: {
    form: {
      orderCode: 'Order Code',
      compCode: 'Company Code',
      compName: 'Company Name',
      suppCode: 'Supplier Code',
      suppName: 'Supplier Name',
      plantCode: 'Plant Code',
      plantName: 'Plant Name',
      plannerNo: 'Planner Group',
      plannerName: 'Planner Group Description',
      createTime: 'Creation Time',
      sapUpdateTime: 'SAP Update Time',
      receiveTime: 'Receive Time',
      isRead: 'Is Read',
      isComplete: 'Is Complete',
      timeBegin: 'Time Window Start',
      timeEnd: 'Time Window End',
      itemNo: 'Line Number',
      purDocType: 'Purchase Document Type',
      itemType: 'Item Category',
      text: 'Purchase Order Header',
      delIden: 'Delete Indicator',
      shortText: 'Purchase Material Description',
      oldArticleNo: 'Old Material Number',
      articleNo: 'Material Number',
      articleName: 'Material Name',
      deliveryDate: 'Delivery Date',
      quantity: 'Quantity',
      unit: 'Unit',
      workbinNo: 'Bin Type',
      workbinName: 'Bin Type Description',
      qtyPerPack: 'Standard Packaging Quantity',
      unloadingNo: 'Unload Loc No.',
      unloadingName: 'Unload Dest.',
      state: 'Status',
      netPrice: 'Net Price',
      priceUnit: 'Price Unit',
      orderNetWorth: 'Order Net Worth',
      currencyCode: 'Currency',
      stockLoc: 'Stock Location',
      locDes: 'Stock Location Description',
      locAdd: 'Stock Location Address',
      rcvName: 'Receiver Name',
      rcvTel: 'Receiver Phone',
      inspeStrategy: 'Inspection Strategy',
      zipCode: 'Postal Code',
      city: 'City',
      countryCode: 'Country Code',
      addTimeZone: 'Address Time Zone',
      street2: 'Street 2',
      street3: 'Street 3',
      street4: 'Street 4',
      depot: 'Depot'
    },
    placeholder: {
      orderCode: 'Please enter order code',
      compCode: 'Please enter company code',
      compName: 'Please enter company name',
      suppCode: 'Please enter supplier code',
      suppName: 'Please enter supplier name',
      plantCode: 'Please enter plant code',
      plantName: 'Please enter plant name',
      plannerNo: 'Please enter planner group',
      plannerName: 'Please enter planner group description',
      startTime: 'Start time',
      endTime: 'End time',
      select: 'Please select',
      itemNo: 'Please enter line number',
      purDocType: 'Please enter purchase document type',
      itemType: 'Please enter item category',
      text: 'Please enter purchase order header',
      delIden: 'Please enter delete indicator',
      shortText: 'Please enter purchase material description',
      oldArticleNo: 'Please enter old material number',
      articleNo: 'Please enter material number',
      articleName: 'Please enter material name',
      deliveryDate: 'Please enter delivery date',
      quantity: 'Please enter quantity',
      unit: 'Please enter unit',
      workbinNo: 'Please enter bin type',
      workbinName: 'Please enter bin type description',
      qtyPerPack: 'Please enter standard packaging quantity',
      unloadingNo: 'Please enter Unload Loc No.',
      unloadingName: 'Please enter Unload Dest.',
      state: 'Please enter status',
      netPrice: 'Please enter net price',
      priceUnit: 'Please enter price unit',
      orderNetWorth: 'Please enter order net worth',
      currencyCode: 'Please enter currency',
      stockLoc: 'Please enter stock location',
      locDes: 'Please enter stock location description',
      locAdd: 'Please enter stock location address',
      rcvName: 'Please enter receiver name',
      rcvTel: 'Please enter receiver phone',
      inspeStrategy: 'Please enter inspection strategy',
      zipCode: 'Please enter postal code',
      city: 'Please enter city',
      countryCode: 'Please enter country code',
      addTimeZone: 'Please enter address time zone',
      street2: 'Please enter street 2',
      street3: 'Please enter street 3',
      street4: 'Please enter street 4',
      depot: 'Please enter the depot'
    },
    table: {
      orderCode: 'Order Code',
      compCode: 'Company Code',
      compName: 'Company Name',
      suppCode: 'Supplier Code',
      suppName: 'Supplier Name',
      plantCode: 'Plant Code',
      plantName: 'Plant Name',
      unloadingNo: 'Unload Loc No.',
      unloadingName: 'Unload Dest.',
      plannerNo: 'Planner Group',
      plannerName: 'Planner Group Description',
      customerCode: 'Customer Code',
      requester: 'Requester',
      createTime: 'Creation Time',
      sapUpdateTime: 'Update Time',
      receiveTime: 'Receive Time',
      isRead: 'Is Read',
      isComplete: 'Is Complete',
      timeBegin: 'Time Window Start',
      timeEnd: 'Time Window End',
      index: 'Index',
      actions: 'Actions',
      status: 'Status',
      downloadingStatus: 'Downloading Status',
      lastDownloadingTime: 'Last Downloading Time'
    },
    button: {
      search: 'Search',
      reset: 'Reset',
      createASN: 'Create ASN',
      printItemTag: 'Print Item Tag',
      printTxt: 'Download TXT',
      printSelectedRows: 'Download Selected Rows',
      confirmSelectedRows: 'Confirm Selected Rows',
      printPartsInstructionNote: 'Print Parts Instruction Note',
      export: 'Export',
      add: 'Add',
      delete: 'Delete',
      confirm: 'Confirm',
      cancel: 'Cancel',
      notification: 'Notification',
      warning: 'Warning'
    },
    divider: {
      orderItems: 'Order Line Item Information'
    },

    title: {
      add: 'Add Order',
      update: 'Update Order'
    },
    success: {
      add: 'Successfully Added',
      update: 'Successfully Updated',
      delete: 'Successfully Deleted'
    },
    confirm: {
      delete:
        'Are you sure you want to delete the data item with order code "{orderIds}"?',
    },
    alert: {
      deleteItem:
        'Please select the order line item data you want to delete first',
      selectOrder: 'Please select at least one order',
      sameClient: 'The selected orders must be for the same client',
      sameSupplier: 'The selected orders must be from the same supplier',
      samePlant: 'The selected orders must be for the same plant',
      sameDepot: 'The selected orders must have the same depot',
      sameUnloading: 'The selected orders must have the same unloading location',
      orderCompleted: 'Cannot create ASN for completed orders',
      znbsNotAllowed: 'Operation Forbidden! This purchase order is for drop shipment business.'
    },
    rules: {
      compCode: 'Company code cannot be empty',
      compName: 'Company name cannot be empty',
      plantCode: 'Plant code cannot be empty',
      plantName: 'Plant name cannot be empty',
      suppCode: 'Supplier code cannot be empty',
      suppName: 'Supplier name cannot be empty',
      plannerNo: 'Planner group cannot be empty',
      plannerName: 'Planner group description cannot be empty',
      orderCode: 'Order code cannot be empty'
    },
    orderDetail: {
      baseInfoTitle: 'Order Information',
      itemsInfoTitle: 'Line Item Information',
      orderCode: 'Order Code',
      compCode: 'Company Code',
      compName: 'Company Name',
      plantCode: 'Plant Code',
      plantName: 'Plant Name',
      plannerNo: 'Planner Group',
      plannerName: 'Planner Group Description',
      suppCode: 'Supplier Code',
      suppName: 'Supplier Name',
      itemNo: 'Line Number',
      articleNo: 'Material Number',
      articleName: 'Material Name',
      quantity: 'Quantity',
      unit: 'Unit',
      netPrice: 'Purchase Price',
      priceUnit: 'Price Unit',
      currencyCode: 'Currency',
      deliveryDate: 'Delivery Date',
      qtyPerPack: 'Standard Packaging Quantity',
      deliverySplit: 'Split Count',
      workbinNo: 'Bin Type',
      workbinName: 'Bin Type Description',
      state: 'Status',
      purDocType: 'Purchase Document Type',
      itemType: 'Item Category',
      text: 'Purchase Order Header',
      orderNetWorth: 'Order Net Worth in Purchase Order Currency',
      delIden: 'Delete Indicator in Purchase Document',
      shortText: 'Purchase Material Description',
      oldArticleNo: 'Old Material Number',
      unloadingNo: 'Unload Loc No.',
      unloadingName: 'Unload Dest.',
      stockLoc: 'Stock Location',
      locDes: 'Stock Location Description',
      locAdd: 'Stock Location Address',
      rcvName: 'Receiver Name',
      rcvTel: 'Receiver Phone',
      inspeStrategy: 'Inspection Strategy',
      zipCode: 'Postal Code',
      city: 'City',
      countryCode: 'Country Code',
      addTimeZone: 'Address Time Zone',
      street2: 'Street 2',
      street3: 'Street 3',
      street4: 'Street 4',
      title: 'Title',
      content: 'Content',
      customerOrderCode: 'Customer Order Number',
      customerOrderLineCode: 'Customer Order Line Number',
      customerDeliveryDate: 'Customer Delivery Date',
      productType: 'Production Method',
      rcvType: 'Receiving Method',
      purchaseType: 'Self-Procurement Type',
      depot: 'Depot'
    }
  },
  asn: {
    asnCode: 'ASN Code',
    enterAsnCode: 'Please enter ASN code',
    compCode: 'Company Code',
    enterCompCode: 'Please enter company code',
    compName: 'Company Name',
    enterCompName: 'Please enter company name',
    suppCode: 'Supplier Code',
    enterSuppCode: 'Please enter supplier code',
    suppName: 'Supplier Name',
    enterSuppName: 'Please enter supplier name',
    status: 'Status',
    planDeliveryDate: 'Planned Delivery Date',
    deliveryDate: 'Shipping Date',
    startDate: 'Start Date',
    endDate: 'End Date',
    search: 'Search',
    reset: 'Reset',
    actionsText: 'Actions',
    delete: 'Delete',
    editText: 'Edit',
    validation: {
      asnCodeRequired: 'ASN code cannot be empty',
      docnoRequired: 'Document number cannot be empty',
      compCodeRequired: 'Company code cannot be empty',
      compNameRequired: 'Company name cannot be empty',
      suppCodeRequired: 'Supplier code cannot be empty',
      suppNameRequired: 'Supplier name cannot be empty',
      docDateRequired: 'Document date cannot be empty',
      dnnoRequired: 'Delivery note number cannot be empty',
      planDeliveryDateRequired: 'Planned delivery date cannot be empty',
      deliveryDateRequired: 'Shipping date cannot be empty',
      quantityUsedUp: 'This item has been used up and cannot be saved'
    },
    actions: {
      add: 'Add ASN',
      editSuccess: 'Successfully Updated',
      addSuccess: 'Successfully Added',
      confirmDelete:
        "Are you sure you want to delete the data item with ASN code '{asnCode}'?",
      deleteSuccess: 'Successfully Deleted',
      confirmExport: 'Are you sure you want to export all ASN data items?',
      warningTitle: 'Warning',
      confirm: 'Confirm',
      cancel: 'Cancel'
    },
    upload: {
      title: 'ASN Import',
      resultTitle: 'Import Result'
    },
    detail: {
      asnInfo: 'ASN Information',
      title: 'Title',
      content: 'Content',
      printDeliveryNote: 'Print Delivery Note',
      printPickingList: 'Print Picking List',
      lineItemInfo: 'Line Item Information',
      dnNo: 'ASN',
      orderCode: 'Purchase Order Number',
      plantCode: 'Plant Code',
      plantName: 'Plant Name',
      unloadingNo: 'Unload Loc No.',
      unloadingName: 'Unload Dest.',
      sendLocNo: 'Sending Location Number',
      sendLocName: 'Sending Location Name',
      rcvLocNo: 'Receiving Location Number',
      rcvLocName: 'Receiving Location Name',
      asnCode: 'ASN Number',
      compCode: 'Company Number',
      compName: 'Company Name',
      planDeliveryDate: 'Planned Delivery Date',
      deliveryDate: 'Shipping Date',
      suppCode: 'Supplier Number',
      suppName: 'Supplier Name'
    },
    article: {
      materialInfo: 'Material Information',
      releaseNo: 'Delivery Schedule No',
      orderLineNo: 'Order Line Number',
      articleNo: 'Material Number',
      articleName: 'Material Name',
      quantity: 'Delivery Quantity',
      unit: 'Unit',
      batchNo: 'Batch Number',
      qtyPerPack: 'Quantity per Pack',
      packQty: 'Number of Packs',
      startWith: 'Barcode Start',
      endWith: 'Barcode End',
      nonStd: 'Non-Standard'
    },
    edit: {
      asnInfo: 'ASN Information',
      asnCode: 'ASN Code',
      enterAsnCode: 'Please enter ASN code',
      compCode: 'Company Code',
      enterCompCode: 'Please enter company code',
      compName: 'Company Name',
      enterCompName: 'Please enter company name',
      suppCode: 'Supplier Code',
      enterSuppCode: 'Please enter supplier code',
      suppName: 'Supplier Name',
      enterSuppName: 'Please enter supplier name',
      planDeliveryDate: 'Planned Delivery Date',
      selectPlanDeliveryDate: 'Select Planned Delivery Date',
      deliveryDate: 'Shipping Date',
      selectDeliveryDate: 'Select Shipping Date',
      save: 'Save',
      send: 'Send',
      cancel: 'Cancel',
      lineItemInfo: 'Line Item Information',
      articleInfo: 'Material Information',
      addArticle: 'Add',
      orderLineNo: 'Order Line Number',
      deliveryScheduleNo: 'Delivery Schedule Number',
      articleNo: 'Material Number',
      articleName: 'Material Name',
      quantity: 'Delivery Quantity',
      unit: 'Unit',
      batchNo: 'Batch Number',
      qtyPerPack: 'Quantity per Pack',
      packQty: 'Number of Packs',
      nonStd: 'Non-Standard',
      actions: 'Actions',
      delete: 'Delete',
      dnNo: 'ASN',
      orderCode: 'Purchase Order Number',
      plantCode: 'Plant Code',
      plantName: 'Plant Name',
      unloadingNo: 'Unload Loc No.',
      unloadingName: 'Unload Dest.',
      sendLocNo: 'Sending Location Number',
      sendLocName: 'Sending Location Name',
      rcvLocNo: 'Receiving Location Number',
      rcvLocName: 'Receiving Location Name',
      selectOrderItem: 'Select Order Line Item',
      itemNo: 'Line Number',
      netPrice: 'Purchase Price',
      priceUnit: 'Price Unit',
      currencyCode: 'Currency',
      confirm: 'Confirm',
      dialog: {
        qtyPerPack: 'Standard Packaging Quantity',
        workbinNo: 'Bin Type',
        workbinName: 'Bin Type Description',
        state: 'Status',
        purDocType: 'Purchase Document Type',
        itemType: 'Item Category',
        text: 'Purchase Order Header',
        orderNetWorth: 'Order Net Worth in Purchase Order Currency',
        delIden: 'Delete Indicator in Purchase Document',
        shortText: 'Purchase Material Description',
        oldArticleNo: 'Old Material Number',
        unloadingNo: 'Unload Loc No.',
        unloadingName: 'Unload Dest.',
        stockLoc: 'Stock Location',
        locDes: 'Stock Location Description',
        locAdd: 'Stock Location Address',
        rcvName: 'Receiver Name',
        rcvTel: 'Receiver Phone',
        inspeStrategy: 'Inspection Strategy',
        zipCode: 'Postal Code',
        city: 'City',
        countryCode: 'Country Code',
        addTimeZone: 'Address Time Zone',
        street2: 'Street 2',
        street3: 'Street 3',
        street4: 'Street 4'
      },
      validation: {
        planDeliveryDateRequired: 'Planned delivery date cannot be empty',
        deliveryDateRequired: 'Shipping date cannot be empty',
        batchNoRequired: 'Batch number cannot be empty',
        quantityRequired: 'Delivery quantity cannot be empty',
        qtyPerPackRequired: 'Quantity per pack cannot be empty',
        quantityUsedUp: 'This item has been used up and cannot be saved',
        affectedOrders: 'Affected orders'
      },
      confirmCancel:
        'Any unsaved changes will be lost. Are you sure you want to cancel?',
      warning: 'Warning',
      atLeastOneLineItem: 'At least one line item is required',
      atLeastOneArticle: 'At least one material information is required',
      maxPackQtyExceeded: 'The total number of packs cannot exceed {max}',
      sendSuccess: 'Successfully Sent',
      createSuccess: 'Successfully Created',
      saveSuccess: 'Successfully Saved'
    },
    button: {
      printPickList: 'Print Pick List',
      printNpsSls: 'Print Delivery Note / Receiving Report',
      printPalletTag: 'Print Pallet Tag'
    },
    alert: {
      selectOnePrint: 'Please select the ASN to print'
    }
  },
  feedback: {
    dnNo: 'ASN Code/Receiving Identifier',
    enterDnNo: 'Please enter delivery note number',
    compCode: 'Company Code',
    enterCompCode: 'Please enter company code',
    compName: 'Company Name',
    enterCompName: 'Please enter company name',
    status: 'status',
    invoiceTax: 'Invoice Tax',
    enterInvoiceTax: 'Please enter invoice tax',
    invoiceNo: 'Invoice No',
    enterInvoiceNo: 'Please enter invoice No',
    invoiceDate: 'Invoice Date',
    enterInvoiceDate: 'Please enter invoice date',
    totalAmount: 'Total Amount',
    currency: 'Currency',
    receivingDate: 'Receiving Date',
    deliveryNoteDate: 'Delivery Note Date',
    downloadStatus: 'Download Status',
    delFlag: 'Delete Flag',
    suppCode: 'Supplier Code',
    enterSuppCode: 'Please enter supplier code',
    suppName: 'Supplier Name',
    enterSuppName: 'Please enter supplier name',
    plantCode: 'Plant Code',
    enterPlantCode: 'Please enter plant code',
    plantName: 'Plant Name',
    enterPlantName: 'Please enter plant name',
    search: 'Search',
    reset: 'Reset',
    export: 'Export',
    confirm: 'Confirm',
    cancel: 'Cancel',
    actionsText: 'Actions',
    viewText: 'View ASN',
    addFeedback: 'Add Receiving Feedback',
    updateSuccess: 'Successfully Updated',
    addSuccess: 'Successfully Added',
    confirmDelete:
      "Are you sure you want to delete receiving feedback with ID '{feedId}'?",
    warning: 'Warning',
    deleteSuccess: 'Successfully Deleted',
    confirmExport:
      'Are you sure you want to export all receiving feedback data?',
    asnCodeNotFound: 'ASN code does not match',
    updateInvoiceInfo: 'Edit Invoice Information',
    alert: {
      deleteItem: 'Please select the row item you want to delete first.',
      selectAtLeastOne: 'Please select at least one row item.',
      sameClient: 'The selected row items must be for the same client.',
      inValidInvoiceInfo: 'The invoice information is incorrect. Please check and resubmit.'
    },
    validation: {
      dnNoRequired: 'Delivery note number cannot be empty',
      compCodeRequired: 'Company code cannot be empty',
      compNameRequired: 'Company name cannot be empty',
      plantCodeRequired: 'Plant code cannot be empty',
      plantNameRequired: 'Plant name cannot be empty',
      suppCodeRequired: 'Supplier code cannot be empty',
      suppNameRequired: 'Supplier name cannot be empty'
    },
    detail: {
      receiptFeedbackInfo: 'Receiving Feedback Information',
      title: 'Title',
      content: 'Content',
      lineItemInfo: 'Line Item Information',
      orderCode: 'Purchase Order Number',
      orderLineNo: 'Purchase Order Line Number',
      articleNo: 'Material Code',
      articleName: 'Material Name',
      rcvDate: 'Receiving Date',
      rcvTime: 'Time',
      quantity: 'Delivery Quantity',
      unit: 'Unit',
      rcvDocNo: 'Receiving Document Number',
      articleDocAnnual: 'Material Document Year',
      rcvDocItemNo: 'Receiving Document Line Number',
      dnNo: 'ASN',
      suppCode: 'Supplier Code',
      suppName: 'Supplier Name',
      plantCode: 'Plant Code',
      plantName: 'Plant Name',
      compCode: 'Company Code',
      compName: 'Company Name'
    },
    button: {
      downloadExcel: 'Feedback Download',
      confirmFeedback: 'Feedback Confirm',
      edit: 'Edit',
      save: 'Save',
      cancel: 'Cancel'
    }
  },
  forecast: {
    form: {
      forecastCode: 'Forecast Code',
      inputForecastCode: 'Please enter forecast code',
      version: 'Forecast Version',
      inputVersion: 'Please enter forecast version',
      compCode: 'Company Code',
      inputCompCode: 'Please enter company code',
      compName: 'Company Name',
      enterCompName: 'Please enter company name',
      suppCode: 'Supplier Code',
      inputSuppCode: 'Please enter supplier code',
      suppName: 'Supplier Name',
      inputSuppName: 'Please enter supplier name',
      plantCode: 'Plant Code',
      inputPlantCode: 'Please enter plant code',
      plantName: 'Plant Name',
      inputPlantName: 'Please enter plant name',
      search: 'Search',
      reset: 'Reset',
      export: 'Export',
      downloadExcel: 'Download Excel',
      forecastConfirm: 'Forecast Confirm'
    },
    table: {
      forecastCode: 'Forecast Code',
      compCode: 'Company Code',
      compName: 'Company Name',
      suppCode: 'Supplier Code',
      suppName: 'Supplier Name',
      plantCode: 'Plant Code',
      plantName: 'Plant Name',
      status: 'Status',
      downloadStatus: 'Download Status',
      lastDownloadTime: 'Last Download Time'
    },
    alert: {
      selectAtLeastOne: 'Please select at least one row item.',
      notAllNew: 'Forecast number {notNewCode} cannot be confirmed because its status is not “New”.'
    },
    dialog: {
      title: 'Add or Edit Forecast',
      titleAdd: 'Add Forecast',
      titleEdit: 'Edit Forecast',
      forecastCode: 'Forecast Code',
      inputForecastCode: 'Please enter forecast code',
      version: 'Forecast Version',
      inputVersion: 'Please enter forecast version',
      compCode: 'Company Code',
      inputCompCode: 'Please enter company code',
      compName: 'Company Name',
      enterCompName: 'Please enter company name',
      plantCode: 'Plant Code',
      inputPlantCode: 'Please enter plant code',
      plantName: 'Plant Name',
      inputPlantName: 'Please enter plant name',
      suppCode: 'Supplier Code',
      inputSuppCode: 'Please enter supplier code',
      suppName: 'Supplier Name',
      inputSuppName: 'Please enter supplier name',
      confirm: 'Confirm',
      cancel: 'Cancel'
    },
    messages: {
      updateSuccess: 'Successfully Updated',
      addSuccess: 'Successfully Added',
      deleteSuccess: 'Successfully Deleted',
      deleteConfirm:
        'Are you sure you want to delete the forecast with code "{id}"?',
      exportConfirm: 'Are you sure you want to export all forecast data?'
    },

    detail: {
      forecastInfo: 'Forecast Information',
      lineItemInfo: 'Line Item Information',
      title: 'Title',
      content: 'Content',
      forecastCode: 'Forecast Code',
      compCode: 'Company Code',
      compName: 'Company Name',
      plantCode: 'Plant Code',
      plantName: 'Plant Name',
      version: 'Forecast Version',
      suppCode: 'Supplier Code',
      suppName: 'Supplier Name',
      articleNo: 'Material Code',
      articleName: 'Material Name',
      deliveryDate: 'Delivery Date',
      quantity: 'Quantity',
      unit: 'Unit',
      durType: 'Duration Type',
      proType: 'Plan Type',
      poddet: 'Purchase Plan Agreement Number'
    }
  },
  inventory: {
    form: {
      compCode: 'Company Code',
      inputCompCode: 'Please enter company code',
      compName: 'Company Name',
      enterCompName: 'Please enter company name',
      suppCode: 'Supplier Code',
      inputSuppCode: 'Please enter supplier code',
      suppName: 'Supplier Name',
      inputSuppName: 'Please enter supplier name',
      plantCode: 'Plant Code',
      inputPlantCode: 'Please enter plant code',
      plantName: 'Plant Name',
      inputPlantName: 'Please enter plant name',
      updateDate: 'Update Date',
      updateTime: 'Update Time',
      startDate: 'Start Date',
      endDate: 'End Date',
      startTime: 'Start Time',
      endTime: 'End Time',
      search: 'Search',
      reset: 'Reset',
      export: 'Export'
    },
    table: {
      compCode: 'Company Code',
      compName: 'Company Name',
      suppCode: 'Supplier Code',
      suppName: 'Supplier Name',
      plantCode: 'Plant Code',
      plantName: 'Plant Name',
      updateDate: 'Update Date',
      updateTime: 'Update Time',
      actions: 'Actions',
      view: 'View'
    },
    dialog: {
      compCode: 'Company Code',
      inputCompCode: 'Please enter company code',
      compName: 'Company Name',
      enterCompName: 'Please enter company name',
      plantCode: 'Plant Code',
      inputPlantCode: 'Please enter plant code',
      plantName: 'Plant Name',
      inputPlantName: 'Please enter plant name',
      suppCode: 'Supplier Code',
      inputSuppCode: 'Please enter supplier code',
      suppName: 'Supplier Name',
      inputSuppName: 'Please enter supplier name',
      updateDate: 'Update Date',
      selectUpdateDate: 'Select Update Date',
      confirm: 'Confirm',
      cancel: 'Cancel',
      addTitle: 'Add Consignment Inventory'
    },
    validation: {
      compCodeRequired: 'Company code cannot be empty',
      compNameRequired: 'Company name cannot be empty',
      plantCodeRequired: 'Plant code cannot be empty',
      plantNameRequired: 'Plant name cannot be empty',
      suppCodeRequired: 'Supplier code cannot be empty',
      suppNameRequired: 'Supplier name cannot be empty',
      updateDateRequired: 'Update date cannot be empty',
      updateTimeRequired: 'Update time cannot be empty'
    },
    messages: {
      updateSuccess: 'Successfully Updated',
      addSuccess: 'Successfully Added',
      deleteConfirm:
        'Are you sure you want to delete the consignment inventory with code "{id}"?',
      deleteSuccess: 'Successfully Deleted',
      exportConfirm:
        'Are you sure you want to export all consignment inventory data?'
    },
    detail: {
      consignmentInfo: 'Consignment Inventory Information',
      lineItemInfo: 'Line Item Information',
      title: 'Title',
      content: 'Content',
      compCode: 'Company Code',
      compName: 'Company Name',
      plantCode: 'Plant Code',
      plantName: 'Plant Name',
      suppCode: 'Supplier Code',
      suppName: 'Supplier Name',
      updateDate: 'Update Date',
      updateTime: 'Update Time',
      articleNo: 'Material Code',
      articleName: 'Material Name',
      quantity: 'Quantity',
      unit: 'Unit',
      days: 'Days',
      remark: 'Remark'
    }
  },
  consignment: {
    form: {
      compCode: 'Company Code',
      inputCompCode: 'Please enter company code',
      compName: 'Company Name',
      enterCompName: 'Please enter company name',
      suppCode: 'Supplier Code',
      inputSuppCode: 'Please enter supplier code',
      suppName: 'Supplier Name',
      inputSuppName: 'Please enter supplier name',
      plantCode: 'Plant Code',
      inputPlantCode: 'Please enter plant code',
      plantName: 'Plant Name',
      inputPlantName: 'Please enter plant name',
      updateDate: 'Update Date',
      updateTime: 'Update Time',
      startDate: 'Start Date',
      endDate: 'End Date',
      startTime: 'Start Time',
      endTime: 'End Time',
      search: 'Search',
      reset: 'Reset',
      export: 'Export'
    },
    table: {
      compCode: 'Company Code',
      compName: 'Company Name',
      suppCode: 'Supplier Code',
      suppName: 'Supplier Name',
      plantCode: 'Plant Code',
      plantName: 'Plant Name',
      updateDate: 'Update Date',
      updateTime: 'Update Time',
      actions: 'Actions',
      view: 'View'
    },
    dialog: {
      compCode: 'Company Code',
      inputCompCode: 'Please enter company code',
      compName: 'Company Name',
      enterCompName: 'Please enter company name',
      plantCode: 'Plant Code',
      inputPlantCode: 'Please enter plant code',
      plantName: 'Plant Name',
      inputPlantName: 'Please enter plant name',
      suppCode: 'Supplier Code',
      inputSuppCode: 'Please enter supplier code',
      suppName: 'Supplier Name',
      inputSuppName: 'Please enter supplier name',
      updateDate: 'Update Date',
      selectUpdateDate: 'Select Update Date',
      confirm: 'Confirm',
      cancel: 'Cancel',
      addTitle: 'Add Consignment Inventory'
    },
    validation: {
      compCodeRequired: 'Company code cannot be empty',
      compNameRequired: 'Company name cannot be empty',
      plantCodeRequired: 'Plant code cannot be empty',
      plantNameRequired: 'Plant name cannot be empty',
      suppCodeRequired: 'Supplier code cannot be empty',
      suppNameRequired: 'Supplier name cannot be empty',
      updateDateRequired: 'Update date cannot be empty',
      updateTimeRequired: 'Update time cannot be empty'
    },
    messages: {
      updateSuccess: 'Successfully Updated',
      addSuccess: 'Successfully Added',
      deleteConfirm:
        'Are you sure you want to delete the consignment inventory with code "{id}"?',
      deleteSuccess: 'Successfully Deleted',
      exportConfirm:
        'Are you sure you want to export all consignment inventory data?'
    },
    detail: {
      consignmentInfo: 'Consignment Inventory Information',
      lineItemInfo: 'Line Item Information',
      title: 'Title',
      content: 'Content',
      compCode: 'Company Code',
      compName: 'Company Name',
      plantCode: 'Plant Code',
      plantName: 'Plant Name',
      suppCode: 'Supplier Code',
      suppName: 'Supplier Name',
      updateDate: 'Update Date',
      updateTime: 'Update Time',
      articleNo: 'Material Code',
      articleName: 'Material Name',
      quantity: 'Quantity',
      unit: 'Unit',
      days: 'Days',
      remark: 'Remark'
    }
  },
  system: {
    user: {
      deptPlaceholder: 'Please enter supplier name',
      usernameLabel: 'Username',
      usernamePlaceholder: 'Please enter username',
      phoneLabel: 'Phone Number',
      phonePlaceholder: 'Please enter phone number',
      statusLabel: 'Status',
      statusPlaceholder: 'User status',
      creationTimeLabel: 'Creation Time',
      dateRangeSeparator: '-',
      startDatePlaceholder: 'Start Date',
      endDatePlaceholder: 'End Date',
      searchButton: 'Search',
      resetButton: 'Reset',
      addButton: 'Add',
      editButton: 'Edit',
      deleteButton: 'Delete',
      importButton: 'Import',
      exportButton: 'Export',
      userId: 'User ID',
      userName: 'Username',
      nickName: 'Nickname',
      department: 'Supplier',
      carrier: 'Carrier',
      phone: 'Phone Number',
      status: 'Status',
      creationTime: 'Creation Time',
      actions: 'Actions',
      resetPasswordButton: 'Reset Password',
      nickNameLabel: 'Nickname',
      nickNamePlaceholder: 'Please enter nickname',
      departmentLabel: 'Supplier',
      departmentPlaceholder: 'Please select supplier',
      emailLabel: 'Email',
      emailPlaceholder: 'Please enter email',
      passwordLabel: 'Password',
      passwordPlaceholder: 'Please enter password',
      genderLabel: 'Gender',
      genderPlaceholder: 'Please select',
      positionLabel: 'Carrier',
      positionPlaceholder: 'Please select carrier',
      roleLabel: 'Role',
      rolePlaceholder: 'Please select',
      remarkLabel: 'Remark',
      remarkPlaceholder: 'Please enter content',
      dragUpload: 'Drag the file here, or',
      clickUpload: 'Click to upload',
      updateExistingUsers: 'Do you want to update existing user data?',
      downloadTemplate: 'Download Template',
      uploadTip:
        'Tip: Only "xls" or "xlsx" format files are allowed to be imported!',
      enable: 'Enable',
      disable: 'Disable',
      confirmChangeStatus: 'Confirm to "{text}" the user "{user}"?',
      statusSuccess: '{text} success',
      addUser: 'Add User',
      editUser: 'Edit User',
      resetPasswordPrompt: 'Please enter the new password for "{user}"',
      resetPasswordSuccess:
        'Successfully updated, the new password is: {password}',
      editSuccess: 'User updated successfully',
      addSuccess: 'User added successfully',
      deleteConfirm: 'Are you sure you want to delete the user "{user}"?',
      deleteSuccess: 'User deleted successfully',
      exportConfirm: 'Are you sure you want to export all user data?',
      importUser: 'User Import',
      importResult: 'Import Result',
      validation: {
        usernameRequired: 'Username cannot be empty',
        nickNameRequired: 'Nickname cannot be empty',
        passwordRequired: 'Password cannot be empty',
        emailInvalid: 'Please enter a valid email address',
        phoneInvalid: 'Please enter a valid phone number'
      },
      profile: {
        personalInfo: 'Personal Information',
        userName: 'Username',
        phone: 'Phone Number',
        email: 'Email',
        department: 'Supplier',
        role: 'Role',
        creationDate: 'Creation Date',
        basicInfo: 'Basic Information',
        basicInfoTab: 'Basic Information',
        changePasswordTab: 'Change Password',
        resetPwd: {
          title: 'Reset Password',
          oldPassword: 'Current Password',
          oldPasswordPlaceholder: 'Please enter current password',
          newPassword: 'New Password',
          newPasswordPlaceholder: 'Please enter new password',
          confirmPassword: 'Confirm Password',
          confirmPasswordPlaceholder: 'Please confirm new password',
          saveButton: 'Save',
          closeButton: 'Close',
          oldPasswordRequired: 'Please enter current password',
          newPasswordRequired: 'Please enter new password',
          passwordLength: 'Length must be between 6 to 20 characters',
          confirmPasswordRequired: 'Please confirm new password',
          passwordMismatch: 'The two passwords do not match',
          updateSuccess: 'Password updated successfully',
          passwordMinLength: 'Password must be at least 8 characters long',
          complexityRequirement: 'Password must contain at least three types of the following: uppercase letters, lowercase letters, numbers, and special characters',
          noConsecutiveChars: 'Password cannot contain the same character three or more times in a row'
        },
        userAvatar: {
          clickToUpload: 'Click to upload avatar',
          dialogTitle: 'Change Avatar',
          selectButton: 'Select',
          submitButton: 'Submit',
          fileError:
            'Invalid file format, please upload an image file like JPG or PNG.',
          uploadSuccess: 'Successfully updated'
        },
        userinfo: {
          nickName: 'Nickname',
          phoneNumber: 'Phone Number',
          email: 'Email',
          gender: 'Gender',
          male: 'Male',
          female: 'Female',
          save: 'Save',
          close: 'Close',
          nickNameRequired: 'Nickname cannot be empty',
          emailRequired: 'Email cannot be empty',
          invalidEmail: 'Please enter a valid email address',
          phoneRequired: 'Phone number cannot be empty',
          invalidPhone: 'Please enter a valid phone number',
          success: 'Successfully updated'
        }
      }
    },
    role: {
      roleName: 'Role Name',
      enterRoleName: 'Please enter role name',
      roleKey: 'Role Key',
      enterRoleKey: 'Please enter role key',
      status: 'Status',
      roleStatus: 'Role Status',
      createTime: 'Creation Time',
      sapUpdateTime: 'Update Time',
      receiveTime: 'Receive Time',
      rangeSeparator: '-',
      startDate: 'Start Date',
      endDate: 'End Date',
      search: 'Search',
      reset: 'Reset',
      add: 'Add',
      update: 'Edit',
      delete: 'Delete',
      export: 'Export',
      roleId: 'Role ID',
      roleSort: 'Display Order',
      actions: 'Actions',
      dataScope: 'Data Scope',
      menuPermission: 'Menu Permissions',
      expandCollapse: 'Expand/Collapse',
      selectAllNone: 'Select All/None',
      linkage: 'Parent-child linkage',
      loading: 'Loading, please wait',
      remark: 'Remark',
      enterContent: 'Please enter content',
      confirm: 'Confirm',
      cancel: 'Cancel',
      dataPermissions: 'Data Permissions',
      allDataPermissions: 'All Data Permissions',
      customDataPermissions: 'Custom Data Permissions',
      departmentDataPermissions: 'Supplier Data Permissions',
      departmentAndSubDataPermissions:
        'Supplier and Sub-supplier Data Permissions',
      personalDataPermissions: 'Personal Data Permissions',
      roleNameRequired: 'Role name cannot be empty',
      roleKeyRequired: 'Role key cannot be empty',
      roleSortRequired: 'Role sort cannot be empty',
      enable: 'Enable',
      disable: 'Disable',
      confirmStatusChange:
        'Are you sure you want to "{text}" the role "{roleName}"?',
      warning: 'Warning',
      statusChangeSuccess: '{text} success',
      addRole: 'Add Role',
      updateRole: 'Edit Role',
      assignDataPermissions: 'Assign Data Permissions',
      updateSuccess: 'Successfully updated',
      addSuccess: 'Successfully added',
      confirmDelete:
        'Are you sure you want to delete the role with ID "{roleIds}"?',
      deleteSuccess: 'Successfully deleted',
      exportConfirm: 'Are you sure you want to export all role data?'
    },
    supplier: {
      supplierCode: 'Supplier Code',
      enterSupplierCode: 'Please enter supplier code',
      supplierName: 'Supplier Name',
      enterSupplierName: 'Please enter supplier name',
      orderNum: 'Order Number',
      leader: 'Leader',
      enterLeader: 'Please enter leader',
      phone: 'Phone Number',
      enterPhone: 'Please enter phone number',
      email: 'Email',
      enterEmail: 'Please enter email',
      status: 'Status',
      selectStatus: 'Select supplier status',
      parentSupplier: 'Parent Supplier',
      selectParentSupplier: 'Select parent supplier',
      addSupplier: 'Add Supplier',
      editSupplier: 'Edit Supplier',
      addSuccess: 'Added successfully',
      editSuccess: 'Edited successfully',
      deleteSuccess: 'Deleted successfully',
      confirmDelete: 'Are you sure you want to delete supplier "{name}"?',
      parentSupplierRequired: 'Parent supplier is required',
      supplierCodeRequired: 'Supplier code is required',
      supplierNameRequired: 'Supplier name is required',
      orderNumRequired: 'Order number is required',
      invalidEmail: 'Please enter a valid email address',
      invalidPhone: 'Please enter a valid phone number'
    },
    carrier: {
      title: 'Carrier Management',
      code: 'Carrier Code',
      name: 'Carrier Name',
      sort: 'Sort Order',
      status: 'Status',
      createTime: 'Create Time',
      remark: 'Remark',
      id: 'Carrier ID',
      placeholder: {
        code: 'Please enter carrier code',
        name: 'Please enter carrier name',
        status: 'Please select carrier status',
        sort: 'Please enter sort order',
        remark: 'Please enter remark'
      },
      add: 'Add Carrier',
      edit: 'Edit Carrier',
      delete: 'Are you sure to delete carrier code "{postIds}"?',
      export: 'Are you sure to export all carrier data?',
      validation: {
        nameRequired: 'Carrier name cannot be empty',
        codeRequired: 'Carrier code cannot be empty',
        sortRequired: 'Sort order cannot be empty'
      }
    },
    common: {
      search: 'Search',
      reset: 'Reset',
      add: 'Add',
      edit: 'Edit',
      delete: 'Delete',
      export: 'Export',
      actions: 'Actions',
      remark: 'Remark',
      inputContent: 'Please enter content',
      confirm: 'Confirm',
      cancel: 'Cancel',
      loading: 'Loading, please wait',
      expandCollapse: 'Expand/Collapse',
      selectAllNone: 'Select All/None',
      parentChildLinkage: 'Parent-child linkage',
      rangeSeparator: '-',
      startDate: 'Start Date',
      endDate: 'End Date',
      createTime: 'Creation Time',
      sapUpdateTime: 'Update Time',
      receiveTime: 'Receive Time',
      creationTime: 'Creation Time',
      warning: 'Warning',
      prompt: 'Prompt',
      success: 'Success',
      createSuccess: 'Created successfully',
      updateSuccess: 'Updated successfully',
      deleteSuccess: 'Deleted successfully',
      id: 'ID'
    }
  },
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    warning: 'Warning',
    prompt: 'Prompt',
    success: 'Success',
    noData: 'No Data'
  },
  errorPage404: {
    error: '404 Error!',
    message: 'Page not found!',
    info: 'Sorry, the page you are looking for does not exist. Try checking the URL for errors, then hit the refresh button on your browser or try finding something else in our application.',
    goHome: 'Go Home',
    notFound: 'Page not found!'
  },
  errorPage: {
    back: 'Go Back',
    error401: '401 Error!',
    noPermission: 'You do not have permission to access!',
    apology:
      'Sorry, you do not have access. Please do not perform any unauthorized operations! You can return to the homepage.',
    goHome: 'Go to Homepage',
    imageAlt: 'The girl’s ice cream fell.'
  },
  setting: {
    themeSettings: 'Theme Style Settings',
    themeColor: 'Theme Color',
    systemLayout: 'System Layout Configuration',
    enableTopNav: 'Enable TopNav',
    enableTagsView: 'Enable Tags-Views',
    fixedHeader: 'Fixed Header',
    showLogo: 'Show Logo',
    saveConfig: 'Save Configuration',
    resetConfig: 'Reset Configuration',
    saving: 'Saving to local, please wait...',
    clearing: 'Clearing settings cache and refreshing, please wait...'
  },
  rightToolbar: {
    showSearch: 'Show Search',
    hideSearch: 'Hide Search',
    refresh: 'Refresh',
    showHideColumns: 'Show/Hide Columns',
    showHide: 'Show/Hide',
    show: 'Show',
    hide: 'Hide'
  },
  navbar: {
    dashboard: 'Dashboard',
    github: 'GitHub',
    logOut: 'Log Out',
    profile: 'Profile',
    theme: 'Change Skin',
    size: 'Layout Size'
  },
  login: {
    title: 'DataLink Management System Login',
    username: {
      placeholder: 'Username',
      empty: 'Username cannot be empty'
    },
    password: {
      placeholder: 'Password',
      empty: 'Password cannot be empty'
    },
    verificationCode: {
      placeholder: 'Verification Code',
      empty: 'Verification code cannot be empty'
    },
    rememberPwd: 'Remember Password',
    logIn: 'Log In',
    loggingIn: 'Logging In...'
  },
  settings: {
    title: 'System Layout Configuration',
    theme: 'Theme Color',
    tagsView: 'Enable Tags-View',
    fixedHeader: 'Fixed Header',
    sidebarLogo: 'Sidebar Logo'
  },
  tagsView: {
    refresh: 'Refresh',
    close: 'Close',
    closeOthers: 'Close Others',
    closeAll: 'Close All'
  },
  notification: {
    logout: {
      title: 'Prompt',
      message: 'Are you sure you want to log out and exit the system?'
    },
    ok: 'OK',
    cancel: 'Cancel'
  },
  language: {
    message: 'Language switch successful'
  },
  utils: {
    request: {
      sessionExpired:
        'The login session has expired. You can stay on this page or log in again.',
      systemPrompt: 'System Prompt',
      relogin: 'Re-login',
      cancel: 'Cancel',
      networkError: 'Backend API connection error',
      requestTimeout: 'System interface request timeout',
      requestError: 'System interface {statusCode} error'
    }
  },
  supplyPlan: {
    plan: {
      title: 'Supply Plan',
      tableTitle: 'Supply Plan Table',
      lastMonthRemaining: 'Previous Month Balance',
      monthlyTotal: 'Monthly Total',
      week1: 'Week 1',
      week2: 'Week 2',
      week3: 'Week 3',
      week4: 'Week 4',
      week5: 'Week 5',
      week6: 'Week 6',
      week7: 'Week 7',
      planAmount: 'Planned Quantity',
      actualAmount: 'Actual Quantity',
      days: {
        mon: 'Mon',
        tue: 'Tue',
        wed: 'Wed',
        thu: 'Thu',
        fri: 'Fri',
        sat: 'Sat',
        sun: 'Sun'
      }
    },
    form: {
      yearMonth: 'Year/Month',
      client: 'Client',
      clientPlaceholder: 'Enter Client Name',
      depot: 'Depot',
      depotPlaceholder: 'Enter Depot',
      factory: 'Required Factory',
      factoryPlaceholder: 'Enter a Factory',
      partNumber: 'Part Number',
      partNumberPlaceholder: 'Enter Part Number'
    },
    factories: {
      factory1: 'Factory 1',
      factory2: 'Factory 2',
      factory3: 'Factory 3'
    },
    buttons: {
      search: 'Search',
      reset: 'Reset'
    },
    info: {
      basicInfo: 'Basic Information',
      client: 'Client',
      depot: 'Depot',
      factory: 'Factory',
      partNumber: 'Part Number',
      requirement: 'Required Quantity',
      nMonth: 'Current Month',
      n1Month: 'Next Month',
      n2Month: 'Month After Next',
      monthly: 'Monthly Plan',
      daily: 'Daily Plan',
      deliveryMethod: 'Delivery Method',
      supplyType: 'Supply Category',
      supplyMethod: 'Supply Method',
      deliveryLocation: 'Delivery Location',
      basicUnit: 'Basic Unit',
      orderUnit: 'Order Unit',
      allocationLot: 'Procurement Lot',
      snep: 'SNEP',
      currentYearMonth: 'Year/Month',
      clientInventory: 'Client Inventory',
      confirmedDate: 'Confirmed Date',
      updatedDateTime: 'Last Update Date & Time'
    }
  },
  internalForecast: {
    title: 'Plan Internal Forecast Download',
    buttons: {
      downloadWeekly: 'Download Weekly Forecast',
      downloadThreeMonths: 'Download 3-Month Forecast',
      downloadYearly: 'Download Annual Forecast',
      downloadWeeklyTxt: 'Download Weekly Forecast TXT',
      downloadThreeMonthsTxt: 'Download Three Months Forecast TXT',
      downloadYearlyTxt: 'Download Annual Forecast TXT'
    },
    badge: {
      undownloaded: 'Undownloaded'
    }
  },
  loadProposalVehicle: {
    function: 'Function',
    functionSearch: 'Search',
    functionStatusChange: 'Status Change',
    functionShipment: 'Register Shipment',
    status: 'Status',
    statusAll: 'All',
    statusDelivered: 'Delivered',
    statusConfirmed: 'Confirmed',
    materialFactor: 'Material Factor',
    materialP: 'P',
    materialQ: 'Q',
    materialF: 'F',
    transportCompany: 'Transport Company',
    client: 'Client',
    pickupDate: 'Pickup Date-Time',
    deliveryDate: 'Delivery Date-Time',
    depot: 'Depot',
    factory: 'Factory',
    deliveryLocation: 'Delivery Location',
    ticketNo: 'Ticket No',
    search: 'Search',
    reload: 'Reload',
    clear: 'Clear',
    addRow: 'Add Row',
    deleteSelected: 'Delete Selected',
    to: 'To',
    startDate: 'Start Date',
    endDate: 'End Date',
    addRowWarning: 'Please click the confirm button to save the data!',
    placeholder: {
      depot: 'Please enter the warehouse',
      factory: 'Please enter the factory',
      deliveryLocation: 'Please enter the delivery location',
      ticketNo: 'Please enter the freight bill number',
      materialFactorPlaceholder: 'Please enter the material factor'
    }
  },
  loadProposalVehicleTable: {
    transportPlanId: 'No',
    status: 'Status',
    statusDraft: 'Draft',
    statusConfirmed: 'Confirmed',
    statusInTransit: 'In Transit',
    statusAssigned: 'Assigned',
    statusCompleted: 'Completed',
    statusDelivered: 'Delivered',
    materialFactor: 'Material Factor',
    way: 'Method',
    pickupDateTime: 'Pickup Date & Time',
    pickupDate: 'Pickup Date',
    pickupTime: 'Pickup Time',
    clientCode: 'Client Code',
    depot: 'Depot',
    deliveryLocation: 'Delivery Location',
    factory: 'Factory',
    deliveryDateTime: 'Delivery Date & Time',
    deliveryDate: 'Delivery Date',
    deliveryTime: 'Delivery Time',
    port: 'Departure Port',
    weight: 'Weight',
    weightTotal: 'Total Weight',
    palletQuantity: 'Pallet Quantity',
    totalQuantity: 'Total Quantity',
    average: 'Average',
    ticketNo: 'Ticket No',
    transportPlan: 'Transport Plan',
    company: 'Transport Company',
    pickupTimeShort: 'Pickup Time',
    pickupQuantity: 'Pickup Quantity',
    carType: 'Vehicle Type',
    carNo: 'Vehicle No',
    driver: 'Driver',
    deliveryLocationName: 'Delivery Location Name',
    companyName: 'Company Name',
    recordMaintenance: 'Record Maintenance',
    registrationInfo: 'Registration Info',
    createDate: 'Creation Date',
    createTime: 'Creation Time',
    createBy: 'Created By',
    updateInfo: 'Update Info',
    updateDate: 'Update Date',
    updateTime: 'Update Time',
    updateBy: 'Updated By',
    actions: 'Actions',
    confirm: 'Confirm',
    smallCar: 'Small Car',
    mediumCar: 'Medium Car',
    largeCar: 'Large Car',
    placeholder: {
      depot: 'Please enter the warehouse',
      factory: 'Please enter the factory',
      deliveryLocation: 'Please enter the delivery location',
      ticketNo: 'Please enter the freight bill number',
      materialFactorPlaceholder: 'Please enter the material factor'
    }
  },
  kanban: {
    form: {
      demandCode: 'Demand Code',
      issueNo: 'Issue No.',
      deliveryInstructionDate: 'Delivery Instruction Date',
      deliveryInstructionTime: 'Delivery Instruction Time',
      deliveryInstructionYy: 'Delivery Instruction Date Time',
      maker: 'Maker',
      depot: 'Depot',
      customerPartsNo: 'Customer Parts No.',
      deliveryTicketNo: 'Delivery Ticket No.'
    },
    placeholder: {
      demandCode: 'Please enter demand code',
      issueNo: 'Please enter issue number',
      deliveryInstructionDate: 'Please select delivery instruction date',
      deliveryInstructionTime: 'Please select delivery instruction time',
      deliveryInstructionYy: 'Please enter delivery instruction date time',
      maker: 'Please enter maker',
      depot: 'Please enter depot',
      customerPartsNo: 'Please enter customer parts number',
      deliveryTicketNo: 'Please enter delivery ticket number'
    },
    table: {
      demandCode: 'Demand Code',
      issueNo: 'Issue No.',
      deliveryInstructionDate: 'Delivery Instruction Date',
      deliveryInstructionTime: 'Delivery Instruction Time',
      deliveryInstructionYy: 'Delivery Instruction Date Time',
      maker: 'Maker',
      depot: 'Depot',
      customerPartsNo: 'Customer Parts No.',
      deliveryTicketNo: 'Delivery Ticket No.'
    },
    button: {
      search: 'Search',
      reset: 'Reset',
      printSvSpec: 'Print Delivery Instructions (SV Spec)',
      printLineKdSpec: 'Print Delivery Instructions (Line KD Spec)',
      printLineSpec: 'Print Delivery Instructions (Line Spec)'
    },
    alert: {
      selectKanban: 'Please select at least one kanban record'
    }
  }
}
