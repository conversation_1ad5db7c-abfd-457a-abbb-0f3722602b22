export default {
  acceptanceDetailOverview: {
    form: {
      suppCode: 'サプライヤーコード',
      dateRange: '日付範囲'
    },
    placeholder: {
      suppCode: 'サプライヤーコードを入力してください',
      startDate: '開始日',
      endDate: '終了日'
    },
    table: {
      settlementNo: '精算伝票番号',
      suppCode: 'サプライヤーコード',
      suppName: 'サプライヤー名',
      companyCode: '会社コード',
      operateTime: '操作時間',
      operateType: '操作タイプ',
      operator: '操作者'
    }
  },
  forecastOverview: {
    form: {
      suppCode: 'サプライヤーコード',
      dateRange: '日付範囲'
    },
    placeholder: {
      suppCode: 'サプライヤーコードを入力してください',
      startDate: '開始日',
      endDate: '終了日'
    },
    table: {
      sendTime: '送信時間',
      suppCode: 'サプライヤーコード',
      suppName: 'サプライヤー名',
      plant: '工場',
      operateTime: '操作時間',
      operateType: '操作タイプ',
      operator: '操作者'
    }
  },
  orderOverview: {
    form: {
      suppCode: 'サプライヤーコード',
      plant: '工場',
      dateRange: '日付範囲'
    },
    placeholder: {
      suppCode: 'サプライヤーコードを入力してください',
      plant: '工場を入力してください',
      startDate: '開始日',
      endDate: '終了日'
    },
    table: {
      suppCode: 'サプライヤーコード',
      suppName: 'サプライヤー名',
      orderNo: '注文番号',
      plant: '工場',
      operateTime: '操作時間',
      operateType: '操作タイプ',
      operator: '操作者'
    }
  },
  palletMaintain: {
    form: {
      plant: '工場',
      partNo: '品目番号'
    },
    placeholder: {
      plant: '工場を入力してください',
      partNo: '品目番号を入力してください'
    },
    button: {
      search: '検索',
      reset: 'リセット',
      edit: '編集',
      save: '保存',
      cancel: 'キャンセル'
    },
    table: {
      index: '番号',
      action: '操作',
      plant: '工場',
      partNo: '品目番号',
      partDesc: '品目説明',
      qtyPerPack: '単位包装数',
      palletSnpQty: 'パレットSNP数量',
      palletLength: 'パレット長さ',
      palletWidth: 'パレット幅',
      palletHeight: 'パレット高さ',
      containerType: '容器種類'
    },
    alert: {
      saveBeforeEdit: '先に現在の編集行を保存またはキャンセルしてください',
      saveSuccess: '保存が成功しました'
    }
  },
  internalForecastDownloadNew: {
    form: {
      suppCode: 'サプライヤーコード',
      createTime: '作成時間',
      status: '状態'
    },
    placeholder: {
      suppCode: 'サプライヤーコードを入力してください',
      status: '状態を選択してください',
      startTime: '開始時間',
      endTime: '終了時間'
    },
    button: {
      search: '検索',
      reset: 'リセット',
      printSelectedRows: '選択行を印刷',
      confirmSelectedRows: '選択行を確定'
    },
    table: {
      index: '番号',
      createTime: '作成時間',
      suppCode: 'サプライヤーコード',
      plantCode: '工場',
      status: '状態',
      downloadingStatus: 'ダウンロード状況',
      lastDownloadingTime: '最終ダウンロード時間'
    },
    status: {
      notDownloaded: '未ダウンロード',
      downloaded: 'ダウンロード済',
      normal: '正常',
      abnormal: '異常'
    },
    alert: {
      selectOrder: '操作する行を選択してください',
      printSuccess: '印刷成功',
      confirmSuccess: '確定成功'
    },
    common: {
      loading: '読み込み中...'
    }
  },
  deliveryNoteGenerate: {
    form: {
      orderNo: '注文番号',
      suppCode: 'サプライヤーコード',
      shipStatus: '出荷状況',
      dateRange: '日付範囲'
    },
    placeholder: {
      orderNo: '注文番号を入力してください',
      suppCode: 'サプライヤーコードを入力してください',
      shipStatus: '出荷状況を選択してください',
      startTime: '開始日',
      endTime: '終了日'
    },
    button: {
      generate: '納品書/受領書を生成',
      print: '納品書/受領書を印刷',
      edit: '編集',
      save: '保存',
      cancel: 'キャンセル'
    },
    table: {
      index: '番号',
      action: '操作',
      orderNo: '注文番号',
      lineNo: '行番号',
      releaseNo: 'リリース番号',
      partNo: '部品番号',
      partDesc: '部品説明',
      orderQty: '注文数量',
      deliveryQty: '納品数量',
      totalDeliveryQty: '総納品数量',
      qtyPerPack: '1梱包あたり数量',
      deliveryDate: '出荷日',
      plant: '工場',
      unit: '単位'
    },
    alert: {
      selectRow: '操作する行を選択してください',
      generateSuccess: '生成成功',
      printSuccess: '印刷成功',
      saveBeforeEdit: '現在編集中の行を先に保存してください',
      saveSuccess: '保存成功'
    }
  },
  acceptanceDownload: {
    form: {
      suppCode: 'サプライヤーコード',
      status: '状態',
      dateRange: '期間',
    },
    placeholder: {
      suppCode: 'サプライヤーコードを入力してください',
      status: '状態を選択してください',
      startTime: '開始日時',
      endTime: '終了日時'
    },
    button: {
      downloadSelectedRows: '選択行をダウンロード',
      confirmSelectedRows: '選択行を確定',
      edit: '編集',
      save: '保存',
      cancel: 'キャンセル'
    },
    table: {
      index: 'NO',
      action: '操作',
      settlementCode: '精算書番号',
      suppName: 'サプライヤー',
      compCode: '会社コード',
      status: '状態',
      invoiceTotalTax: '請求総税額',
      invoiceNo: '金税請求書番号',
      invoiceDate: '請求日',
      invoiceAmount: '請求金額',
      currency: '通貨',
      receiveDate: '受領日'
    },
    alert: {
      selectRow: '操作する行を選択してください',
      downloadSuccess: 'ダウンロード成功',
      confirmSuccess: '確定成功',
      saveBeforeEdit: '現在編集中の行を先に保存してください',
      saveSuccess: '保存成功'
    }
  },
  ticketPrint: {
    form: {
      orderNo: '注文番号',
      suppCode: 'サプライヤーコード',
      shipStatus: '出荷状況',
      dateRange: '日付範囲'
    },
    placeholder: {
      orderNo: '注文番号を入力してください',
      suppCode: 'サプライヤーコードを入力してください',
      shipStatus: '出荷状況を選択してください',
      startTime: '開始日',
      endTime: '終了日'
    },
    button: {
      printDeliveryAcceptance: '納品書/受領書を印刷',
      printPickList: 'ピッキングリストを印刷',
      printItemTag: '現品票印刷',
      printPalletTag: 'パレットタグを印刷'
    },
    table: {
      index: '番号',
      asnNo: 'ASN番号',
      orderNo: '注文番号',
      lineNo: '行番号',
      releaseNo: 'リリース番号',
      partNo: '部品番号',
      partDesc: '部品説明',
      plant: '工場',
      shipDate: '出荷日',
      orderQty: '注文数量',
      deliveryQty: '納品数量',
      qtyPerPack: '1梱包あたり数量',
      unit: '単位',
      shipStatus: '出荷状況',
      deliveryDate: '受領日',
      warehouse: '倉庫',
      palletCount: 'パレット数',
      containerType: '容器タイプ',
      totalDeliveryQty: '総納品数量'
    },
    alert: {
      selectRow: '操作する行を選択してください',
      printSuccess: '印刷が成功しました'
    },
    dialog: {
      itemTagTitle: '現品票印刷',
      lineNo: '行番号',
      orderNo: '注文番号',
      batchNo: 'バッチ番号',
      addBatchNo: 'バッチ追加',
      removeBatchNo: 'バッチ削除',
      printItemTag: '現品票印刷',
      reprintItemTag: '現品票再印刷',
      selectReprintRow: '再印刷する行を選択してください',
      reprintSuccess: '現品票再印刷が成功しました',
      inputLineOrder: '行番号と注文番号を入力してください',
      noBatchToDelete: '削除できるバッチがありません',
      table: {
        index: '番号',
        orderNo: '注文番号',
        lineNo: '行番号',
        releaseNo: '下達番号',
        partNo: '品目番号',
        partDesc: '品目説明',
        batchNo: 'バッチ',
        snp: 'SNP',
        deliveryQty: '納入数量',
        deliveryDate: '納入日'
      }
    }
  },
  orderDetail: {
    form: {
      orderNo: '注文番号',
      partNo: '部品番号',
      shipmentStatus: '出荷状況',
      orderDate: '注文日',
      startDate: '開始日',
      endDate: '終了日',
    },
    placeholder: {
      orderNo: '注文番号を入力してください',
      partNo: '部品番号を入力してください',
      shipmentStatus: '出荷状況を選択してください',
      startDate: '開始日',
      endDate: '終了日',
    },
    button: {
      search: '検索',
      reset: 'リセット',
    },
    table: {
      orderNo: '注文番号',
      lineNo: '行番号',
      releaseNo: 'リリース番号',
      orderStatus: '注文状況',
      shipmentStatus: '出荷状況',
      partNo: '部品番号',
      partDesc: '部品説明',
      deliveryDate: '納品日',
      orderQty: '注文数量',
      unit: '単位',
    },
    shipmentStatusOptions: {
      not_shipped: '未出荷',
      shipping: '配送中',
      delivered: '納品済み',
    }
  },

  web: {
    title: 'DataLink管理システム'
  },
  loadingMessage: 'システムリソースを読み込んでいます。しばらくお待ちください',
  dashboard: {
    title: '7日間データ集計',
    stats: {
      Order: '注文',
      Forecast: '内示',
      Consignment: '委託在庫',
      Inventory: '在庫',
      Feedback: '受領確認'
    },
    orderAnalysis: '7日間データ集計',
    totalOrders: '総注文数',
    completedOrders: '完了注文',
    pendingOrders: '保留中の注文',
    compareToYesterday: '前日比',
    chart: {
      totalOrders: '総注文数',
      completedOrders: '完了',
      pendingOrders: '保留中'
    }
  },
  route: {
    dashboard: 'ダッシュボード',
    orderDownload: '注文ダウンロード',
    dataManagement: 'データ管理',
    dataReceive: 'データ受信',
    order: '注文',
    forecast: '内示',
    consignmentInventory: '委託在庫',
    inventory: '在庫',
    receiptFeedback: '受領確認',
    ASN番号: 'ASN番号',
    dataSend: 'データ送信',
    systemManagement: 'システム管理',
    userManagement: 'ユーザー管理',
    roleManagement: 'ロール管理',
    menuManagement: 'メニュー管理',
    supplierManagement: 'サプライヤ管理',
    postManagement: '運送業者管理',
    dictionaryManagement: '辞書管理',
    parameterSettings: 'パラメータ設定',
    notice: '通知',
    logManagement: 'ログ管理',
    operationLog: '操作ログ',
    loginLog: 'ログインログ',
    systemMonitoring: 'システム監視',
    onlineUsers: 'オンラインユーザー',
    scheduledTasks: 'スケジュールタスク',
    dataMonitoring: 'データ監視',
    serverMonitoring: 'サーバー監視',
    cacheMonitoring: 'キャッシュ監視',
    systemTools: 'システムツール',
    formBuilder: 'フォームビルダー',
    codeGeneration: 'コード生成',
    systemInterface: 'システムインターフェース',
    profile: '個人情報',
    dictData: '辞書データ',
    dispatchLog: 'ディスパッチログ',
    modifyGeneratedConfig: '生成設定の変更',
    orderDetail: '注文の詳細',
    purchaseOrderDetail: '購入注文の詳細',
    acceptanceDownload: '検収明細ダウンロード',
    deliveryNoteGenerate: '納品書／受領書を作成',
    ticketPrint: 'チケット印刷',
    palletMaintain: 'パレットメンテナンス',
    orderOverview: '注文概要',
    forecastDetail: '内示の詳細',
    forecastOverview: '内示概要',
    acceptanceDetailOverview: '検収明細概要',
    consignmentInventoryDetail: '委託在庫の詳細',
    inventoryDetail: '在庫の詳細',
    receiptFeedbackDetail: '受領確認の詳細',
    ASNDetail: 'ASNの詳細',
    ASNEdit: 'ASNの編集',
    internalForecast: '内示',
    loadProposalVehicleRegistration: '荷量提示配車登録',
    loadProposalVehicleConfirmation: '荷量提示配車確認',
    supplyPlan: '支給計画',
    kanban: '単機能連携',
    ePartner: 'e-Partner',
    kanbaA8245: '単機能連携デ-タ(ライン)',
    kanbaM4028: '単機能連携デ-タ(SV)',
    kanbaN2396: '单機能連携デ-タ(KD)',
    downloadFunction: 'ダウンロード機能',
    printFunction: '印刷機能',
    overview: '概览'
  },
  order: {
    form: {
      orderCode: '注文番号',
      compCode: '会社コード',
      compName: '会社名',
      suppCode: 'サプライヤコード',
      suppName: 'サプライヤ名',
      plantCode: '工場コード',
      plantName: '工場名',
      plannerNo: '調達グループ',
      plannerName: '調達グループの説明',
      createTime: '初回作成日時',
      sapUpdateTime: '更新日時',
      receiveTime: '受信日時',
      isRead: '既読',
      isComplete: '完了',
      timeBegin: '時間ウィンドウ開始',
      timeEnd: '時間ウィンドウ終了',
      itemNo: '行番号',
      purDocType: '発注区分',
      itemType: 'アイテムカテゴリ',
      text: '調達注文ヘッダー',
      delIden: '削除識別',
      shortText: '調達品の説明',
      oldArticleNo: '旧品番',
      articleNo: '部品番号',
      articleName: '部品名称',
      deliveryDate: '納期',
      quantity: '数量',
      unit: '単位',
      workbinNo: '荷姿コード',
      workbinName: '荷姿コードの説明',
      qtyPerPack: 'SNP',
      unloadingNo: '納入場所',
      unloadingName: '納入先',
      state: '状態',
      netPrice: '調達価格',
      priceUnit: '価格単位',
      orderNetWorth: '注文の純価値',
      currencyCode: '通貨',
      stockLoc: '保管場所',
      locDes: '保管場所の説明',
      locAdd: 'Location',
      rcvName: '在庫管理者',
      rcvTel: '在庫管理者電話番号',
      inspeStrategy: '検査戦略',
      zipCode: '郵便番号',
      city: '市区町村',
      countryCode: '国コード',
      addTimeZone: '住所のタイムゾーン',
      street2: '番地2',
      street3: '番地3',
      street4: '番地4',
      depot: 'デポ'
    },
    placeholder: {
      orderCode: '注文番号を入力してください',
      compCode: '会社コードを入力してください',
      compName: '会社名を入力してください',
      suppCode: 'サプライヤコードを入力してください',
      suppName: 'サプライヤ名を入力してください',
      plantCode: '工場コードを入力してください',
      plantName: '工場名を入力してください',
      plannerNo: '調達グループを入力してください',
      plannerName: '調達グループの説明を入力してください',
      startTime: '開始時間',
      endTime: '終了時間',
      select: '選択してください',
      itemNo: '行番号を入力してください',
      purDocType: '発注区分を入力してください',
      itemType: 'アイテムカテゴリを入力してください',
      text: '調達注文ヘッダーを入力してください',
      delIden: '削除識別を入力してください',
      shortText: '調達品の説明を入力してください',
      oldArticleNo: '旧品番を入力してください',
      articleNo: '品番を入力してください',
      articleName: '品名を入力してください',
      deliveryDate: '納期を入力してください',
      quantity: '数量を入力してください',
      unit: '単位を入力してください',
      workbinNo: '荷姿コードを入力してください',
      workbinName: '荷姿コードの説明を入力してください',
      qtyPerPack: 'SNPを入力してください',
      unloadingNo: '納入場所を入力してください',
      unloadingName: '納入先を入力してください',
      state: '状態を入力してください',
      netPrice: '調達価格を入力してください',
      priceUnit: '価格単位を入力してください',
      orderNetWorth: '注文の純価値を入力してください',
      currencyCode: '通貨を入力してください',
      stockLoc: '保管場所を入力してください',
      locDes: '保管場所の説明を入力してください',
      locAdd: '所在地を入力してください',
      rcvName: '在庫管理者を入力してください',
      rcvTel: '在庫管理者電話番号を入力してください',
      inspeStrategy: '検査戦略を入力してください',
      zipCode: '郵便番号を入力してください',
      city: '市区町村を入力してください',
      countryCode: '国コードを入力してください',
      addTimeZone: '住所のタイムゾーンを入力してください',
      street2: '番地2を入力してください',
      street3: '番地3を入力してください',
      street4: '番地4を入力してください',
      depot: 'デポを入力してください'
    },
    table: {
      orderCode: '注文番号',
      compCode: '会社コード',
      compName: '会社名',
      suppCode: 'サプライヤコード',
      suppName: 'サプライヤ名',
      downloadStatus: 'ダウンロード状況',
      plantCode: '工場コード',
      plantName: '工場名',
      unloadingNo: '納入場所',
      unloadingName: '納入先',
      customerCode: '顧客コード',
      requester: '発注元',
      plannerNo: '調達グループ',
      plannerName: '調達グループの説明',
      createTime: '初回作成日時',
      sapUpdateTime: '更新日時',
      receiveTime: '受信日時',
      isRead: '既読',
      isComplete: '完了',
      timeBegin: '時間ウィンドウ開始',
      timeEnd: '時間ウィンドウ終了',
      index: 'インデックス',
      actions: '操作'
    },
    button: {
      search: '検索',
      reset: 'リセット',
      createASN: 'ASN作成',
      orderConfirm: '注文確認',
      printItemTag: '現品票印刷',
      printTxt: 'TXTダウンロード',
      orderDownload: '注文ダウンロード',
      printPartsInstructionNote: '部品納入指示書印刷',
      export: 'エクスポート',
      add: '追加',
      delete: '削除',
      confirm: '確認',
      cancel: 'キャンセル',
      notification: '通知',
      warning: '警告'
    },
    divider: {
      orderItems: '注文の行アイテム情報'
    },
    title: {
      add: '注文の追加',
      update: '注文の更新'
    },
    success: {
      add: '追加に成功しました',
      update: '更新に成功しました',
      delete: '削除に成功しました',
      confirm: '確認に成功しました'
    },
    confirm: {
      delete: '注文番号が"{orderIds}"のデータを削除してもよろしいですか？',
      confirmAllItems:
        '全部の商品を選ばなくても、注文全体が更新されます。よろしいですか？',
      downloadAllItems:
        '全部の商品を選ばなくても、注文全体がダウンロードされます。よろしいですか？'
    },
    alert: {
      deleteItem: '削除する注文行データをまず選択してください',
      selectOrder: '少なくとも1件の注文を選択してください',
      onlyNewCanConfirm: '注文番号が {notNewOrderCodes} のステータスは「New」ではないため、確認不可',
      sameClient: '選択された注文は同一の顧客のものでなければなりません',
      sameSupplier: '選択された注文は同一のサプライヤのものでなければなりません',
      samePlant: '選択された注文は同一の工場のものでなければなりません',
      sameDepot: '選択された注文は同一のデポのものでなければなりません',
      sameUnloading: '選択された注文は同一の納入場所のものでなければなりません',
      orderCompleted: '完了した注文のASNは作成できません',
      znbsNotAllowed: 'オーダー＊は売買両建てのため操作禁止'
    },
    rules: {
      compCode: '会社コードは必須です',
      compName: '会社名を入力してください',
      plantCode: '工場コードは必須です',
      plantName: '工場名は必須です',
      suppCode: 'サプライヤコードは必須です',
      suppName: 'サプライヤ名は必須です',
      plannerNo: '調達グループは必須です',
      plannerName: '調達グループの説明は必須です',
      orderCode: '注文番号は必須です'
    },
    orderDetail: {
      baseInfoTitle: '注文情報',
      itemsInfoTitle: '行アイテム情報',
      orderCode: '注文番号',
      compCode: '会社コード',
      compName: '会社名',
      plantCode: '工場コード',
      plantName: '工場名',
      plannerNo: '調達グループ',
      plannerName: '調達グループの説明',
      suppCode: 'サプライヤコード',
      suppName: 'サプライヤ名',
      itemNo: '行番号',
      status: '注文の状態',
      releaseNo: 'リリース番号',
      articleNo: '部品番号',
      articleName: '部品名称',
      quantity: '数量',
      unit: '単位',
      netPrice: '調達価格',
      priceUnit: '価格単位',
      currencyCode: '通貨',
      deliveryDate: '納期',
      qtyPerPack: 'SNP',
      deliverySplit: '分割回数',
      workbinNo: '荷姿コード',
      workbinName: '荷姿コードの説明',
      state: '状態',
      purDocType: '発注区分',
      itemType: 'アイテムカテゴリ',
      text: '調達注文ヘッダー',
      orderNetWorth: '調達注文の純価値',
      delIden: '調達文書削除識別',
      shortText: '調達品の説明',
      oldArticleNo: '旧品番',
      unloadingNo: '納入場所',
      unloadingName: '納入先',
      stockLoc: '保管場所',
      locDes: '保管場所の説明',
      locAdd: '所在地',
      rcvName: '在庫管理者',
      rcvTel: '在庫管理者電話番号',
      inspeStrategy: '検査戦略',
      zipCode: '郵便番号',
      city: '市区町村',
      countryCode: '国コード',
      addTimeZone: '住所のタイムゾーン',
      street2: '番地2',
      street3: '番地3',
      street4: '番地4',
      title: 'タイトル',
      content: '内容',
      customerOrderCode: '得意先発行No',
      customerOrderLineCode: '顧客注文行番号',
      customerDeliveryDate: '得意先納期',
      productType: '生産方式',
      rcvType: '納入方式',
      purchaseType: '自己調達区分',
      depot: 'デポ'
    }
  },
  purchaseOrder: {
    orderDetail: {
      baseInfoTitle: '注文情報',
      partsInfoTitle: '部品情報',
      orderCode: '注文番号',
      lineNo: '行番号',
      releaseNo: 'リリース番号',
      suppAddr: 'サプライヤー住所',
      rcvAddr: '受入先住所',
      depot: '倉庫',
      plantName: '工場',
      shipStatus: '出荷状況',
      deliveryDate: '納品日',
      partNo: '部品番号',
      partDesc: '部品説明',
      quantity: '数量',
      orderStatus: '注文状況',
      unit: '単位',
      qtyPerPack: '標準包装数量',
      buyerCode: 'バイヤー番号',
      title: 'タイトル',
      content: '内容'
    },
    form: {
      orderNo: '注文番号',
      orderNoPlaceholder: '注文番号を入力してください',
      partNo: '品番',
      partNoPlaceholder: '品番を入力してください',
      shipStatus: '出荷状況',
      shipStatusPlaceholder: '出荷状況を選択してください',
      dateRange: '日付範囲',
      startDate: '開始日',
      endDate: '終了日',
      to: 'から'
    },
    placeholder: {
      orderNo: '注文番号を入力してください',
      partNo: '品番を入力してください',
      shipStatus: '出荷状況を選択してください',
      dateRange: '日付範囲を選択してください',
      to: 'から',
      startDate: '開始日',
      endDate: '終了日'
    },
    table: {
      orderNo: '注文番号',
      lineNo: '行番号',
      releaseNo: 'リリース番号',
      orderStatus: '注文状況',
      shipStatus: '出荷状況',
      partNo: '品番',
      partDesc: '品名',
      deliveryDate: '納品日',
      orderQty: '注文数量',
      unit: '単位'
    },
    search: '検索',
    button: {
      search: '検索',
      reset: 'リセット',
      createASN: 'ASN作成',
      printItemTag: '現品票印刷',
      printTxt: 'TXTダウンロード',
      printPartsInstructionNote: '部品納入指示書印刷',
      export: 'エクスポート',
      add: '追加',
      delete: '削除',
      confirm: '確認',
      cancel: 'キャンセル',
      notification: '通知',
      warning: '警告'
    },
    divider: {
      orderItems: '注文の行アイテム情報'
    },
    title: {
      add: '注文の追加',
      update: '注文の更新'
    },
    success: {
      add: '追加に成功しました',
      update: '更新に成功しました',
      delete: '削除に成功しました'
    },
    confirm: {
      delete: '注文番号が"{orderIds}"のデータを削除してもよろしいですか？'
    },
    alert: {
      deleteItem: '削除する注文行データをまず選択してください',
      selectOrder: '少なくとも1件の注文を選択してください',
      sameClient: '選択された注文は同一の顧客のものでなければなりません',
      sameSupplier: '選択された注文は同一のサプライヤのものでなければなりません',
      samePlant: '選択された注文は同一の工場のものでなければなりません',
      sameDepot: '選択された注文は同一のデポのものでなければなりません',
      sameUnloading: '選択された注文は同一の納入場所のものでなければなりません',
      orderCompleted: '完了した注文のASNは作成できません',
      znbsNotAllowed: 'オーダー＊は売買両建てのため操作禁止'
    },
    rules: {
      compCode: '会社コードは必須です',
      compName: '会社名を入力してください',
      plantCode: '工場コードは必須です',
      plantName: '工場名は必須です',
      suppCode: 'サプライヤコードは必須です',
      suppName: 'サプライヤ名は必須です',
      plannerNo: '調達グループは必須です',
      plannerName: '調達グループの説明は必須です',
      orderCode: '注文番号は必須です'
    }
  },
  orderDownload: {
    form: {
      orderCode: '注文番号',
      compCode: '会社コード',
      compName: '会社名',
      suppCode: 'サプライヤコード',
      suppName: 'サプライヤ名',
      plantCode: '工場コード',
      plantName: '工場名',
      plannerNo: '調達グループ',
      plannerName: '調達グループの説明',
      createTime: '初回作成日時',
      sapUpdateTime: '更新日時',
      receiveTime: '受信日時',
      isRead: '既読',
      isComplete: '完了',
      timeBegin: '時間ウィンドウ開始',
      timeEnd: '時間ウィンドウ終了',
      itemNo: '行番号',
      purDocType: '発注区分',
      itemType: 'アイテムカテゴリ',
      text: '調達注文ヘッダー',
      delIden: '削除識別',
      shortText: '調達品の説明',
      oldArticleNo: '旧品番',
      articleNo: '部品番号',
      articleName: '部品名称',
      deliveryDate: '納期',
      quantity: '数量',
      unit: '単位',
      workbinNo: '荷姿コード',
      workbinName: '荷姿コードの説明',
      qtyPerPack: 'SNP',
      unloadingNo: '納入場所',
      unloadingName: '納入先',
      state: '状態',
      netPrice: '調達価格',
      priceUnit: '価格単位',
      orderNetWorth: '注文の純価値',
      currencyCode: '通貨',
      stockLoc: '保管場所',
      locDes: '保管場所の説明',
      locAdd: 'Location',
      rcvName: '在庫管理者',
      rcvTel: '在庫管理者電話番号',
      inspeStrategy: '検査戦略',
      zipCode: '郵便番号',
      city: '市区町村',
      countryCode: '国コード',
      addTimeZone: '住所のタイムゾーン',
      street2: '番地2',
      street3: '番地3',
      street4: '番地4',
      depot: 'デポ'
    },
    placeholder: {
      orderCode: '注文番号を入力してください',
      compCode: '会社コードを入力してください',
      compName: '会社名を入力してください',
      suppCode: 'サプライヤコードを入力してください',
      suppName: 'サプライヤ名を入力してください',
      plantCode: '工場コードを入力してください',
      plantName: '工場名を入力してください',
      plannerNo: '調達グループを入力してください',
      plannerName: '調達グループの説明を入力してください',
      startTime: '開始時間',
      endTime: '終了時間',
      select: '選択してください',
      itemNo: '行番号を入力してください',
      purDocType: '発注区分を入力してください',
      itemType: 'アイテムカテゴリを入力してください',
      text: '調達注文ヘッダーを入力してください',
      delIden: '削除識別を入力してください',
      shortText: '調達品の説明を入力してください',
      oldArticleNo: '旧品番を入力してください',
      articleNo: '品番を入力してください',
      articleName: '品名を入力してください',
      deliveryDate: '納期を入力してください',
      quantity: '数量を入力してください',
      unit: '単位を入力してください',
      workbinNo: '荷姿コードを入力してください',
      workbinName: '荷姿コードの説明を入力してください',
      qtyPerPack: 'SNPを入力してください',
      unloadingNo: '納入場所を入力してください',
      unloadingName: '納入先を入力してください',
      state: '状態を入力してください',
      netPrice: '調達価格を入力してください',
      priceUnit: '価格単位を入力してください',
      orderNetWorth: '注文の純価値を入力してください',
      currencyCode: '通貨を入力してください',
      stockLoc: '保管場所を入力してください',
      locDes: '保管場所の説明を入力してください',
      locAdd: '所在地を入力してください',
      rcvName: '在庫管理者を入力してください',
      rcvTel: '在庫管理者電話番号を入力してください',
      inspeStrategy: '検査戦略を入力してください',
      zipCode: '郵便番号を入力してください',
      city: '市区町村を入力してください',
      countryCode: '国コードを入力してください',
      addTimeZone: '住所のタイムゾーンを入力してください',
      street2: '番地2を入力してください',
      street3: '番地3を入力してください',
      street4: '番地4を入力してください',
      depot: 'デポを入力してください'
    },
    table: {
      orderCode: '注文番号',
      compCode: '会社コード',
      compName: '会社名',
      suppCode: 'サプライヤコード',
      suppName: 'サプライヤ名',
      plantCode: '工場コード',
      plantName: '工場名',
      unloadingNo: '納入場所',
      unloadingName: '納入先',
      customerCode: '顧客コード',
      requester: '発注元',
      plannerNo: '調達グループ',
      plannerName: '調達グループの説明',
      createTime: '初回作成日時',
      sapUpdateTime: '更新日時',
      receiveTime: '受信日時',
      isRead: '既読',
      isComplete: '完了',
      timeBegin: '時間ウィンドウ開始',
      timeEnd: '時間ウィンドウ終了',
      index: 'インデックス',
      actions: '操作',
      status: '状態',
      downloadingStatus: 'ダウンロード状態',
      lastDownloadingTime: '最後のダウンロード時間'
    },
    button: {
      search: '検索',
      reset: 'リセット',
      createASN: 'ASN作成',
      printItemTag: '現品票印刷',
      printTxt: 'TXTダウンロード',
      printSelectedRows: '選択行印刷',
      confirmSelectedRows: '選択行確認',
      printPartsInstructionNote: '部品納入指示書印刷',
      export: 'エクスポート',
      add: '追加',
      delete: '削除',
      confirm: '確認',
      cancel: 'キャンセル',
      notification: '通知',
      warning: '警告'
    },
    divider: {
      orderItems: '注文の行アイテム情報'
    },
    title: {
      add: '注文の追加',
      update: '注文の更新'
    },
    success: {
      add: '追加に成功しました',
      update: '更新に成功しました',
      delete: '削除に成功しました'
    },
    confirm: {
      delete: '注文番号が"{orderIds}"のデータを削除してもよろしいですか？'
    },
    alert: {
      deleteItem: '削除する注文行データをまず選択してください',
      selectOrder: '少なくとも1件の注文を選択してください',
      sameClient: '選択された注文は同一の顧客のものでなければなりません',
      sameSupplier: '選択された注文は同一のサプライヤのものでなければなりません',
      samePlant: '選択された注文は同一の工場のものでなければなりません',
      sameDepot: '選択された注文は同一のデポのものでなければなりません',
      sameUnloading: '選択された注文は同一の納入場所のものでなければなりません',
      orderCompleted: '完了した注文のASNは作成できません',
      znbsNotAllowed: 'オーダー＊は売買両建てのため操作禁止'
    },
    rules: {
      compCode: '会社コードは必須です',
      compName: '会社名を入力してください',
      plantCode: '工場コードは必須です',
      plantName: '工場名は必須です',
      suppCode: 'サプライヤコードは必須です',
      suppName: 'サプライヤ名は必須です',
      plannerNo: '調達グループは必須です',
      plannerName: '調達グループの説明は必須です',
      orderCode: '注文番号は必須です'
    },
    orderDetail: {
      baseInfoTitle: '注文情報',
      itemsInfoTitle: '行アイテム情報',
      orderCode: '注文番号',
      compCode: '会社コード',
      compName: '会社名',
      plantCode: '工場コード',
      plantName: '工場名',
      plannerNo: '調達グループ',
      plannerName: '調達グループの説明',
      suppCode: 'サプライヤコード',
      suppName: 'サプライヤ名',
      itemNo: '行番号',
      articleNo: '部品番号',
      articleName: '部品名称',
      quantity: '数量',
      unit: '単位',
      netPrice: '調達価格',
      priceUnit: '価格単位',
      currencyCode: '通貨',
      deliveryDate: '納期',
      qtyPerPack: 'SNP',
      deliverySplit: '分割回数',
      workbinNo: '荷姿コード',
      workbinName: '荷姿コードの説明',
      state: '状態',
      purDocType: '発注区分',
      itemType: 'アイテムカテゴリ',
      text: '調達注文ヘッダー',
      orderNetWorth: '調達注文の純価値',
      delIden: '調達文書削除識別',
      shortText: '調達品の説明',
      oldArticleNo: '旧品番',
      unloadingNo: '納入場所',
      unloadingName: '納入先',
      stockLoc: '保管場所',
      locDes: '保管場所の説明',
      locAdd: '所在地',
      rcvName: '在庫管理者',
      rcvTel: '在庫管理者電話番号',
      inspeStrategy: '検査戦略',
      zipCode: '郵便番号',
      city: '市区町村',
      countryCode: '国コード',
      addTimeZone: '住所のタイムゾーン',
      street2: '番地2',
      street3: '番地3',
      street4: '番地4',
      title: 'タイトル',
      content: '内容',
      customerOrderCode: '得意先発行No',
      customerOrderLineCode: '顧客注文行番号',
      customerDeliveryDate: '得意先納期',
      productType: '生産方式',
      rcvType: '納入方式',
      purchaseType: '自己調達区分',
      depot: 'デポ'
    }
  },
  asn: {
    asnCode: 'ASN番号',
    enterAsnCode: 'ASN番号を入力してください',
    compCode: '会社コード',
    compName: '会社名',
    enterCompCode: '会社コードを入力してください',
    enterCompName: '会社名を入力してください',
    suppCode: 'サプライヤコード',
    enterSuppCode: 'サプライヤコードを入力してください',
    suppName: 'サプライヤ名',
    enterSuppName: 'サプライヤ名を入力してください',
    status: '状態',
    planDeliveryDate: '予定納期',
    deliveryDate: '納期',
    startDate: '開始日',
    endDate: '終了日',
    search: '検索',
    reset: 'リセット',
    actionsText: '操作',
    delete: '削除',
    editText: '編集',
    validation: {
      asnCodeRequired: 'ASN番号は必須です',
      docnoRequired: '文書番号は必須です',
      compCodeRequired: '会社コードは必須です',
      compNameRequired: '会社名は必須です',
      suppCodeRequired: 'サプライヤコードは必須です',
      suppNameRequired: 'サプライヤ名は必須です',
      docDateRequired: '文書日付は必須です',
      dnnoRequired: 'ASN番号は必須です',
      planDeliveryDateRequired: '予定納期は必須です',
      deliveryDateRequired: '納期は必須です',
      quantityUsedUp: 'この品目は使用済みのため保存できません'
    },
    actions: {
      add: 'ASNの追加',
      editSuccess: '更新に成功しました',
      addSuccess: '追加に成功しました',
      confirmDelete: "ASN番号'{asnCode}'のデータを削除してもよろしいですか？",
      deleteSuccess: '削除に成功しました',
      confirmExport: 'すべてのASNデータをエクスポートしてもよろしいですか？',
      warningTitle: '警告',
      confirm: '確認',
      cancel: 'キャンセル'
    },
    upload: {
      title: 'ASNのインポート',
      resultTitle: 'インポート結果'
    },
    detail: {
      asnInfo: 'ASN情報',
      title: 'タイトル',
      content: '内容',
      printDeliveryNote: '納品書印刷',
      printPickingList: 'ピッキングリスト印刷',
      lineItemInfo: '行アイテム情報',
      dnNo: 'ASN番号',
      orderCode: '調達注文番号',
      plantCode: '工場コード',
      plantName: '工場名',
      unloadingNo: '納入場所',
      unloadingName: '納入先',
      sendLocNo: '発送地点番号',
      sendLocName: '発送地点名',
      rcvLocNo: '受領地点番号',
      rcvLocName: '受領地点名',
      asnCode: 'ASN番号',
      compCode: '会社コード',
      compName: '会社名',
      planDeliveryDate: '予定納期',
      deliveryDate: '納期',
      suppCode: 'サプライヤコード',
      suppName: 'サプライヤ名'
    },
    article: {
      materialInfo: '品目情報',
      releaseNo: 'リリース番号',
      orderLineNo: '注文行番号',
      articleNo: '部品番号',
      articleName: '部品名称',
      quantity: '納品数量',
      unit: '単位',
      batchNo: 'ロット番号',
      qtyPerPack: '箱あたり数量',
      packQty: '箱数',
      startWith: 'バーコード開始',
      endWith: 'バーコード終了',
      nonStd: '非標準'
    },
    edit: {
      asnInfo: 'ASN情報',
      asnCode: 'ASN番号',
      enterAsnCode: 'ASN番号を入力してください',
      compCode: '会社コード',
      compName: '会社名',
      enterCompCode: '会社コードを入力してください',
      enterCompName: '会社名を入力してください',
      suppCode: 'サプライヤコード',
      enterSuppCode: 'サプライヤコードを入力してください',
      suppName: 'サプライヤ名',
      enterSuppName: 'サプライヤ名を入力してください',
      planDeliveryDate: '予定納期',
      selectPlanDeliveryDate: '予定納期を選択してください',
      deliveryDate: '納期',
      selectDeliveryDate: '納期を選択してください',
      save: '保存',
      send: '送信',
      cancel: 'キャンセル',
      lineItemInfo: '行アイテム情報',
      articleInfo: '品目情報',
      addArticle: '追加',
      orderLineNo: '注文行番号',
      deliveryScheduleNo: '納品予定番号',
      articleNo: '部品番号',
      articleName: '部品名称',
      quantity: '納品数量',
      unit: '単位',
      batchNo: 'ロット番号',
      qtyPerPack: '箱あたり数量',
      packQty: '箱数',
      nonStd: '非標準',
      actions: '操作',
      delete: '削除',
      dnNo: 'ASN番号',
      orderCode: '調達注文番号',
      plantCode: '工場コード',
      plantName: '工場名',
      unloadingNo: '納入場所番号',
      unloadingName: '納入場所',
      sendLocNo: '発送地点番号',
      sendLocName: '発送地点名',
      rcvLocNo: '受領地点番号',
      rcvLocName: '受領地点名',
      selectOrderItem: '注文行アイテムを選択',
      itemNo: '行番号',
      netPrice: '調達価格',
      priceUnit: '価格単位',
      currencyCode: '通貨',
      confirm: '確認',
      dialog: {
        qtyPerPack: 'SNP',
        workbinNo: '荷姿コード番号',
        workbinName: '荷姿コードの説明',
        state: '状態',
        purDocType: '発注区分',
        itemType: 'アイテムカテゴリ',
        text: '調達注文ヘッダー',
        orderNetWorth: '調達注文の純価値',
        delIden: '調達文書削除識別',
        shortText: '調達品説明',
        oldArticleNo: '旧品番',
        unloadingNo: '納入場所番号',
        unloadingName: '納入場所',
        stockLoc: '保管場所',
        locDes: '保管場所説明',
        locAdd: '所在地',
        rcvName: '受領者名',
        rcvTel: '受領者電話番号',
        inspeStrategy: '検査戦略',
        zipCode: '郵便番号',
        city: '市区町村',
        countryCode: '国コード',
        addTimeZone: '住所のタイムゾーン',
        street2: '番地2',
        street3: '番地3',
        street4: '番地4'
      },
      validation: {
        planDeliveryDateRequired: '予定納期は必須です',
        deliveryDateRequired: '納期は必須です',
        batchNoRequired: 'ロット番号は必須です',
        quantityRequired: '納品数量は必須です',
        qtyPerPackRequired: '箱あたり数量は必須です',
        quantityUsedUp: 'この品目は使用済みのため保存できません',
        affectedOrders: '影響を受ける注文'
      },
      confirmCancel:
        '保存されていない変更は失われます。キャンセルしてもよろしいですか？',
      warning: '警告',
      atLeastOneLineItem: '少なくとも1つの行アイテムが必要です',
      atLeastOneArticle: '少なくとも1つの品目情報が必要です',
      maxPackQtyExceeded: '箱数は{max}を超えることはできません',
      sendSuccess: '送信に成功しました',
      createSuccess: '作成に成功しました',
      saveSuccess: '保存に成功しました'
    },
    button: {
      printPickList: 'ピッキングリストを印刷',
      printNpsSls: '納品書・受領書を印刷',
      printPalletTag: 'パレットタグを印刷'
    },
    alert: {
      selectOnePrint: '印刷するASNを選択してください'
    }
  },
  feedback: {
    dnNo: 'ASN番号/受領識別子',
    enterDnNo: 'ASN番号を入力してください',
    compCode: '会社コード',
    compName: '会社名',
    enterCompCode: '会社コードを入力してください',
    enterCompName: '会社名を入力してください',
    status: '状態',
    invoiceTax: '付加価値税合計',
    enterInvoiceTax: '付加価値税の合計を入力してください',
    invoiceNo: '金税インボイス番号',
    enterInvoiceNo: '金税インボイス番号を入力してください',
    invoiceDate: '請求書発行日',
    enterInvoiceDate: '請求書の発行日を入力してください',
    totalAmount: '請求金額',
    currency: '通貨',
    receivingDate: '受領日',
    deliveryNoteDate: '発送日',
    downloadStatus: 'ダウンロード状態',
    delFlag: '削除フラグ',
    suppCode: 'サプライヤコード',
    enterSuppCode: 'サプライヤコードを入力してください',
    suppName: 'サプライヤ名',
    enterSuppName: 'サプライヤ名を入力してください',
    plantCode: '工場コード',
    enterPlantCode: '工場コードを入力してください',
    plantName: '工場名',
    enterPlantName: '工場名を入力してください',
    search: '検索',
    reset: 'リセット',
    export: 'エクスポート',
    confirm: '確認',
    cancel: 'キャンセル',
    actionsText: '操作',
    viewText: 'ASN表示',
    addFeedback: '受領確認の追加',
    updateSuccess: '更新が成功しました',
    addSuccess: '追加が成功しました',
    confirmDelete: "IDが'{feedId}'の受領確認を削除してもよろしいですか？",
    warning: '警告',
    deleteSuccess: '削除が成功しました',
    confirmExport: 'すべての受領確認データをエクスポートしてもよろしいですか？',
    asnCodeNotFound: 'ASN番号が見つかりません',
    alert: {
      deleteItem: '削除する行項目を先に選択してください。',
      selectAtLeastOne: '少なくとも1つの行項目を選択してください。',
      sameClient: '選択した行項目は同じ顧客である必要があります。',
      inValidInvoiceInfo: '請求書の確認情報に誤りがあります。ご確認の上、再送信してください。'
    },
    validation: {
      dnNoRequired: 'ASN番号は必須です',
      compCodeRequired: '会社コードは必須です',
      compNameRequired: '会社名は必須です',
      plantCodeRequired: '工場コードは必須です',
      plantNameRequired: '工場名は必須です',
      suppCodeRequired: 'サプライヤコードは必須です',
      suppNameRequired: 'サプライヤ名は必須です'
    },
    detail: {
      receiptFeedbackInfo: '受領確認情報',
      title: 'タイトル',
      content: '内容',
      lineItemInfo: '行アイテム情報',
      orderCode: '調達注文番号',
      orderLineNo: '調達注文行番号',
      articleNo: '品目コード',
      articleName: '品目名',
      rcvDate: '受領日',
      rcvTime: '受領時間',
      quantity: '納品数量',
      unit: '単位',
      rcvDocNo: '受領文書番号',
      articleDocAnnual: '品目文書年度',
      rcvDocItemNo: '受領文書行番号',
      dnNo: 'ASN番号',
      suppCode: 'サプライヤコード',
      suppName: 'サプライヤ名',
      plantCode: '工場コード',
      plantName: '工場名',
      compCode: '会社コード',
      compName: '会社名'
    },
    button: {
      downloadExcel: '受領確認をダウンロー'
    }
  },
  forecast: {
    form: {
      forecastCode: '内示番号',
      inputForecastCode: '内示番号を入力してください',
      version: '内示バージョン',
      inputVersion: '内示バージョンを入力してください',
      compCode: '会社コード',
      inputCompCode: '会社コードを入力してください',
      compName: '会社名',
      inputCompName: '会社名を入力してください',
      suppCode: 'サプライヤーコード',
      inputSuppCode: 'サプライヤーコードを入力してください',
      suppName: 'サプライヤー名',
      inputSuppName: 'サプライヤー名を入力してください',
      plantCode: '工場コード',
      inputPlantCode: '工場コードを入力してください',
      plantName: '工場名',
      inputPlantName: '工場名を入力してください',
      search: '検索',
      reset: 'リセット',
      export: 'エクスポート',
      downloadExcel: '内示Excelをダウンロード',
      forecastConfirm: '内示の確認'
    },
    table: {
      forecastCode: '内示番号',
      compCode: '会社コード',
      compName: '会社名',
      suppCode: 'サプライヤーコード',
      suppName: 'サプライヤー名',
      plantCode: '工場コード',
      plantName: '工場名',
      status: '状態',
      downloadStatus: 'ダウンロード状態',
      lastDownloadTime: '最終ダウンロード時刻'
    },
    alert: {
      selectAtLeastOne: '少なくとも1つの行項目を選択してください。',
      notAllNew: '内示番号 {notNewCode} のステータスが「New」ではないため、確認できません'
    },
    dialog: {
      title: '内示の追加または編集',
      titleAdd: '内示の追加',
      titleEdit: '内示の編集',
      forecastCode: '内示番号',
      inputForecastCode: '内示番号を入力してください',
      version: '内示バージョン',
      inputVersion: '内示バージョンを入力してください',
      compCode: '会社コード',
      inputCompCode: '会社コードを入力してください',
      compName: '会社名',
      inputCompName: '会社名を入力してください',
      plantCode: '工場コード',
      inputPlantCode: '工場コードを入力してください',
      plantName: '工場名',
      inputPlantName: '工場名を入力してください',
      suppCode: 'サプライヤーコード',
      inputSuppCode: 'サプライヤーコードを入力してください',
      suppName: 'サプライヤー名',
      inputSuppName: 'サプライヤー名を入力してください',
      confirm: '確認',
      cancel: 'キャンセル'
    },
    messages: {
      updateSuccess: '更新に成功しました',
      addSuccess: '追加に成功しました',
      deleteSuccess: '削除に成功しました',
      deleteConfirm: '内示番号が"{id}"のデータを削除してもよろしいですか？',
      exportConfirm: 'すべての内示データをエクスポートしてもよろしいですか？'
    },

    detail: {
      forecastInfo: '内示情報',
      lineItemInfo: '行アイテム情報',
      title: 'タイトル',
      content: '内容',
      forecastCode: '内示番号',
      compCode: '会社コード',
      compName: '会社名',
      plantCode: '工場コード',
      plantName: '工場名',
      version: '内示バージョン',
      suppCode: 'サプライヤーコード',
      suppName: 'サプライヤー名',
      articleNo: '品目コード',
      articleName: '品目名',
      deliveryDate: '納期',
      quantity: '数量',
      unit: '単位',
      durType: '期間タイプ',
      proType: '計画タイプ',
      poddet: '調達計画契約番号'
    }
  },
  inventory: {
    form: {
      compCode: '会社コード',
      inputCompCode: '会社コードを入力してください',
      compName: '会社名',
      inputCompName: '会社名を入力してください',
      suppCode: 'サプライヤーコード',
      inputSuppCode: 'サプライヤーコードを入力してください',
      suppName: 'サプライヤー名',
      inputSuppName: 'サプライヤー名を入力してください',
      plantCode: '工場コード',
      inputPlantCode: '工場コードを入力してください',
      plantName: '工場名',
      inputPlantName: '工場名を入力してください',
      updateDate: '更新日',
      updateTime: '更新時間',
      startDate: '開始日',
      endDate: '終了日',
      startTime: '開始時間',
      endTime: '終了時間',
      search: '検索',
      reset: 'リセット',
      export: 'エクスポート'
    },
    table: {
      compCode: '会社コード',
      compName: '会社名',
      suppCode: 'サプライヤーコード',
      suppName: 'サプライヤー名',
      plantCode: '工場コード',
      plantName: '工場名',
      updateDate: '更新日',
      updateTime: '更新時間',
      actions: '操作',
      view: '表示'
    },
    dialog: {
      compCode: '会社コード',
      inputCompCode: '会社コードを入力してください',
      compName: '会社名',
      inputCompName: '会社名を入力してください',
      plantCode: '工場コード',
      inputPlantCode: '工場コードを入力してください',
      plantName: '工場名',
      inputPlantName: '工場名を入力してください',
      suppCode: 'サプライヤーコード',
      inputSuppCode: 'サプライヤーコードを入力してください',
      suppName: 'サプライヤー名',
      inputSuppName: 'サプライヤー名を入力してください',
      updateDate: '更新日',
      selectUpdateDate: '更新日を選択してください',
      confirm: '確認',
      cancel: 'キャンセル',
      addTitle: '委託在庫の追加'
    },
    validation: {
      compCodeRequired: '会社コードは空にできません',
      compNameRequired: '会社名は空にできません',
      plantCodeRequired: '工場コードは空にできません',
      plantNameRequired: '工場名は空にできません',
      suppCodeRequired: 'サプライヤーコードは空にできません',
      suppNameRequired: 'サプライヤー名は空にできません',
      updateDateRequired: '更新日は空にできません',
      updateTimeRequired: '更新時間は空にできません'
    },
    messages: {
      updateSuccess: '更新に成功しました',
      addSuccess: '追加に成功しました',
      deleteConfirm: '委託在庫番号が"{id}"のデータを削除してもよろしいですか？',
      deleteSuccess: '削除に成功しました',
      exportConfirm:
        'すべての委託在庫データをエクスポートしてもよろしいですか？'
    },
    detail: {
      consignmentInfo: '委託在庫情報',
      lineItemInfo: '行アイテム情報',
      title: 'タイトル',
      content: '内容',
      compCode: '会社コード',
      compName: '会社名',
      plantCode: '工場コード',
      plantName: '工場名',
      suppCode: 'サプライヤーコード',
      suppName: 'サプライヤー名',
      updateDate: '更新日',
      updateTime: '更新時間',
      articleNo: '品目コード',
      articleName: '品目名',
      quantity: '数量',
      unit: '単位',
      days: '日数',
      remark: '備考'
    }
  },
  consignment: {
    form: {
      compCode: '会社コード',
      inputCompCode: '会社コードを入力してください',
      compName: '会社名',
      inputCompName: '会社名を入力してください',
      suppCode: 'サプライヤーコード',
      inputSuppCode: 'サプライヤーコードを入力してください',
      suppName: 'サプライヤー名',
      inputSuppName: 'サプライヤー名を入力してください',
      plantCode: '工場コード',
      inputPlantCode: '工場コードを入力してください',
      plantName: '工場名',
      inputPlantName: '工場名を入力してください',
      updateDate: '更新日',
      updateTime: '更新時間',
      startDate: '開始日',
      endDate: '終了日',
      startTime: '開始時間',
      endTime: '終了時間',
      search: '検索',
      reset: 'リセット',
      export: 'エクスポート'
    },
    table: {
      compCode: '会社コード',
      compName: '会社名',
      suppCode: 'サプライヤーコード',
      suppName: 'サプライヤー名',
      plantCode: '工場コード',
      plantName: '工場名',
      updateDate: '更新日',
      updateTime: '更新時間',
      actions: '操作',
      view: '表示'
    },
    dialog: {
      compCode: '会社コード',
      inputCompCode: '会社コードを入力してください',

      compName: '会社名',
      inputCompName: '会社名を入力してください',
      plantCode: '工場コード',
      inputPlantCode: '工場コードを入力してください',
      plantName: '工場名',
      inputPlantName: '工場名を入力してください',
      suppCode: 'サプライヤーコード',
      inputSuppCode: 'サプライヤーコードを入力してください',
      suppName: 'サプライヤー名',
      inputSuppName: 'サプライヤー名を入力してください',
      updateDate: '更新日',
      selectUpdateDate: '更新日を選択してください',
      confirm: '確認',
      cancel: 'キャンセル',
      addTitle: '委託在庫の追加'
    },
    validation: {
      compCodeRequired: '会社コードは空にできません',
      compNameRequired: '会社名は空にできません',

      plantCodeRequired: '工場コードは空にできません',
      plantNameRequired: '工場名は空にできません',
      suppCodeRequired: 'サプライヤーコードは空にできません',
      suppNameRequired: 'サプライヤー名は空にできません',
      updateDateRequired: '更新日は空にできません',
      updateTimeRequired: '更新時間は空にできません'
    },
    messages: {
      updateSuccess: '更新に成功しました',
      addSuccess: '追加に成功しました',
      deleteConfirm: '委託在庫番号が"{id}"のデータを削除してもよろしいですか？',
      deleteSuccess: '削除に成功しました',
      exportConfirm:
        'すべての委託在庫データをエクスポートしてもよろしいですか？'
    },
    detail: {
      consignmentInfo: '委託在庫情報',
      lineItemInfo: '行アイテム情報',
      title: 'タイトル',
      content: '内容',
      compCode: '会社コード',

      compName: '会社名',
      plantCode: '工場コード',
      plantName: '工場名',
      suppCode: 'サプライヤーコード',
      suppName: 'サプライヤー名',
      updateDate: '更新日',
      updateTime: '更新時間',
      articleNo: '品目コード',
      articleName: '品目名',
      quantity: '数量',
      unit: '単位',
      days: '日数',
      remark: '備考'
    }
  },
  system: {
    user: {
      deptPlaceholder: '仕入先名を入力してください',
      usernameLabel: 'ユーザー名',
      usernamePlaceholder: 'ユーザー名を入力してください',
      phoneLabel: '電話番号',
      phonePlaceholder: '電話番号を入力してください',
      statusLabel: '状態',
      statusPlaceholder: 'ユーザーの状態を選択してください',
      creationTimeLabel: '作成日時',
      dateRangeSeparator: '-',
      startDatePlaceholder: '開始日',
      endDatePlaceholder: '終了日',
      searchButton: '検索',
      resetButton: 'リセット',
      addButton: '追加',
      editButton: '編集',
      deleteButton: '削除',
      importButton: 'インポート',
      exportButton: 'エクスポート',
      userId: 'ユーザーID',
      userName: 'ユーザー名',
      nickName: 'ニックネーム',
      department: '仕入先',
      carrier: '運送業者',
      phone: '電話番号',
      status: '状態',
      creationTime: '作成日時',
      actions: '操作',
      resetPasswordButton: 'パスワードリセット',
      nickNameLabel: 'ニックネーム',
      nickNamePlaceholder: 'ニックネームを入力してください',
      departmentLabel: '仕入先',
      departmentPlaceholder: '仕入先を選択してください',
      emailLabel: 'メールアドレス',
      emailPlaceholder: 'メールアドレスを入力してください',
      passwordLabel: 'パスワード',
      passwordPlaceholder: 'パスワードを入力してください',
      genderLabel: '性別',
      genderPlaceholder: '性別を選択してください',
      positionLabel: '運送業者',
      positionPlaceholder: '運送業者を選択してください',
      roleLabel: 'ロール',
      rolePlaceholder: 'ロールを選択してください',
      remarkLabel: '備考',
      remarkPlaceholder: '備考を入力してください',
      dragUpload: 'ファイルをここにドラッグするか、',
      clickUpload: 'クリックしてアップロード',
      updateExistingUsers: '既存のユーザーデータを更新しますか？',
      downloadTemplate: 'テンプレートをダウンロード',
      uploadTip:
        'ヒント：インポートは「xls」または「xlsx」形式のファイルのみ許可されます！',
      enable: '有効化',
      disable: '無効化',
      confirmChangeStatus: 'ユーザー"{user}"の状態を"{text}"に変更しますか？',
      statusSuccess: '{text}に成功しました',
      addUser: 'ユーザーの追加',
      editUser: 'ユーザーの編集',
      resetPasswordPrompt: '"{user}"の新しいパスワードを入力してください',
      resetPasswordSuccess:
        'パスワードが正常に更新されました。新しいパスワード：{password}',
      editSuccess: 'ユーザー情報の更新に成功しました',
      addSuccess: 'ユーザーの追加に成功しました',
      deleteConfirm: 'ユーザー"{user}"を削除してもよろしいですか？',
      deleteSuccess: 'ユーザーの削除に成功しました',
      exportConfirm: 'すべてのユーザーデータをエクスポートしてもよろしいですか？',
      importUser: 'ユーザーのインポート',
      importResult: 'インポート結果',
      validation: {
        usernameRequired: 'ユーザー名は必須です',
        nickNameRequired: 'ニックネームは必須です',
        passwordRequired: 'パスワードは必須です',
        emailInvalid: '有効なメールアドレスを入力してください',
        phoneInvalid: '有効な電話番号を入力してください'
      },
      profile: {
        personalInfo: '個人情報',
        userName: 'ユーザー名',
        phone: '電話番号',
        email: 'メールアドレス',
        department: '仕入先',
        role: 'ロール',
        creationDate: '作成日',
        basicInfo: '基本情報',
        basicInfoTab: '基本情報',
        changePasswordTab: 'パスワードの変更',
        resetPwd: {
          title: 'パスワードの変更',
          oldPassword: '現在のパスワード',
          newPassword: '新しいパスワード',
          confirmPassword: '新しいパスワード（確認）',
          oldPasswordPlaceholder: '現在のパスワードを入力してください',
          newPasswordPlaceholder: '新しいパスワードを入力してください',
          confirmPasswordPlaceholder: '新しいパスワードを再入力してください',
          updateSuccess: '更新が完了しました',
          passwordMismatch: 'パスワードが一致しません',
          saveButton: '保存',
          closeButton: '閉じる',
          oldPasswordRequired: '現在のパスワードを入力してください',
          newPasswordRequired: '新しいパスワードを入力してください',
          confirmPasswordRequired: '新しいパスワードを再入力してください',
          passwordMinLength: 'パスワードは8文字以上である必要があります',
          complexityRequirement: 'パスワードは大文字、小文字、数字、特殊文字のうち3種類以上を含む必要があります',
          noConsecutiveChars: '同じ文字を3回以上連続して使用することはできません'
        },
        userAvatar: {
          clickToUpload: 'クリックしてアバターをアップロード',
          dialogTitle: 'アバターの変更',
          selectButton: '選択',
          submitButton: '送信',
          fileError:
            '無効なファイル形式です。JPGまたはPNG形式の画像ファイルをアップロードしてください。',
          uploadSuccess: 'アバターの更新に成功しました'
        },
        userinfo: {
          nickName: 'ニックネーム',
          phoneNumber: '電話番号',
          email: 'メールアドレス',
          gender: '性別',
          male: '男性',
          female: '女性',
          save: '保存',
          close: '閉じる',
          nickNameRequired: 'ニックネームは必須です',
          emailRequired: 'メールアドレスは必須です',
          invalidEmail: '有効なメールアドレスを入力してください',
          phoneRequired: '電話番号は必須です',
          invalidPhone: '有効な電話番号を入力してください',
          success: '更新に成功しました'
        }
      }
    },
    role: {
      roleName: 'ロール名',
      enterRoleName: 'ロール名を入力してください',
      roleKey: '権限キー',
      enterRoleKey: '権限キーを入力してください',
      status: '状態',
      roleStatus: 'ロールの状態',
      createTime: '作成日時',
      sapUpdateTime: 'SAP更新日時',
      receiveTime: '受信日時',
      rangeSeparator: '-',
      startDate: '開始日',
      endDate: '終了日',
      search: '検索',
      reset: 'リセット',
      add: '追加',
      update: '編集',
      delete: '削除',
      export: 'エクスポート',
      roleId: 'ロールID',
      roleSort: '表示順序',
      actions: '操作',
      dataScope: 'データスコープ',
      menuPermission: 'メニュー権限',
      expandCollapse: '展開/折りたたみ',
      selectAllNone: '全選択/全解除',
      linkage: '親子リンク',
      loading: '読み込み中、少々お待ちください',
      remark: '備考',
      enterContent: '内容を入力してください',
      confirm: '確認',
      cancel: 'キャンセル',
      dataPermissions: 'データ権限',
      allDataPermissions: 'すべてのデータ権限',
      customDataPermissions: 'カスタムデータ権限',
      departmentDataPermissions: '仕入先データ権限',
      departmentAndSubDataPermissions: '仕入先およびその下位仕入先のデータ権限',
      personalDataPermissions: '個人データ権限',
      roleNameRequired: 'ロール名は必須です',
      roleKeyRequired: '権限キーは必須です',
      roleSortRequired: 'ロールの順序は必須です',
      enable: '有効化',
      disable: '無効化',
      confirmStatusChange:
        'ロール"{roleName}"を"{text}"に変更してもよろしいですか？',
      warning: '警告',
      statusChangeSuccess: '{text}に成功しました',
      addRole: 'ロールの追加',
      updateRole: 'ロールの編集',
      assignDataPermissions: 'データ権限の割り当て',
      updateSuccess: '更新に成功しました',
      addSuccess: '追加に成功しました',
      confirmDelete: 'ロールID"{roleIds}"を削除してもよろしいですか？',
      deleteSuccess: '削除に成功しました',
      exportConfirm: 'すべてのロールデータをエクスポートしてもよろしいですか？'
    },
    supplier: {
      supplierCode: 'サプライヤーコード',
      enterSupplierCode: 'サプライヤーコードを入力してください',
      supplierName: 'サプライヤー名',
      enterSupplierName: 'サプライヤー名を入力してください',
      orderNum: '順番',
      leader: '担当者',
      enterLeader: '担当者を入力してください',
      phone: '電話番号',
      enterPhone: '電話番号を入力してください',
      email: 'メールアドレス',
      enterEmail: 'メールアドレスを入力してください',
      status: '状態',
      selectStatus: 'サプライヤーの状態を選択してください',
      parentSupplier: '上位サプライヤー',
      selectParentSupplier: '上位サプライヤーを選択してください',
      addSupplier: 'サプライヤーを追加',
      editSupplier: 'サプライヤーを編集',
      addSuccess: '追加に成功しました',
      editSuccess: '編集に成功しました',
      deleteSuccess: '削除に成功しました',
      confirmDelete: 'サプライヤー名 "{name}" を削除してもよろしいですか？',
      parentSupplierRequired: '上位サプライヤーを選択してください',
      supplierCodeRequired: 'サプライヤーコードは必須です',
      supplierNameRequired: 'サプライヤー名は必須です',
      orderNumRequired: '順番は必須です',
      invalidEmail: '正しいメールアドレスを入力してください',
      invalidPhone: '正しい電話番号を入力してください'
    },
    carrier: {
      title: '運送業者管理',
      code: '運送業者コード',
      name: '運送業者名',
      sort: '表示順序',
      status: 'ステータス',
      createTime: '作成日時',
      sapUpdateTime: 'SAP更新日時',
      receiveTime: '受信日時',
      remark: '備考',
      id: '運送業者ID',
      placeholder: {
        code: '運送業者コードを入力してください',
        name: '運送業者名を入力してください',
        status: '運送業者のステータスを選択してください',
        sort: '表示順序を入力してください',
        remark: '備考を入力してください'
      },
      add: '運送業者を追加',
      edit: '運送業者を編集',
      delete: '運送業者コード"{postIds}"のデータを削除してもよろしいですか？',
      export: 'すべての運送業者データをエクスポートしてもよろしいですか？',
      validation: {
        nameRequired: '運送業者名は必須です',
        codeRequired: '運送業者コードは必須です',
        sortRequired: '表示順序は必須です'
      }
    },
    common: {
      search: '検索',
      reset: 'リセット',
      add: '追加',
      edit: '編集',
      delete: '削除',
      export: 'エクスポート',
      actions: '操作',
      remark: '備考',
      inputContent: '内容を入力してください',
      confirm: '確認',
      cancel: 'キャンセル',
      loading: '読み込み中、少々お待ちください',
      expandCollapse: '展開/折りたたみ',
      selectAllNone: '全選択/全解除',
      parentChildLinkage: '親子リンク',
      rangeSeparator: '-',
      startDate: '開始日',
      createTime: '作成日時',
      sapUpdateTime: 'SAP更新日時',
      receiveTime: '受信日時',
      creationTime: '作成日時',
      endDate: '終了日',
      warning: '警告',
      prompt: 'プロンプト',
      success: '成功',
      createSuccess: '作成に成功しました',
      updateSuccess: '更新に成功しました',
      deleteSuccess: '削除に成功しました',
      id: 'ID'
    }
  },
  common: {
    confirm: '確認',
    cancel: 'キャンセル',
    warning: '警告',
    prompt: 'プロンプト',
    success: '成功',
    noData: 'データなし'
  },
  errorPage404: {
    error: '404エラー！',
    message: 'ページが見つかりません！',
    info: '申し訳ありませんが、お探しのページは存在しません。URLに誤りがないか確認して、ブラウザをリフレッシュするか、他のコンテンツを探してください。',
    goHome: 'ホームに戻る',
    notFound: 'ページが見つかりません！'
  },
  errorPage: {
    back: '戻る',
    error401: '401エラー！',
    noPermission: 'アクセス権がありません！',
    apology:
      '申し訳ありませんが、アクセス権がありません。ホームページに戻ってください。',
    goHome: 'ホームに戻る',
    imageAlt: 'アイスクリームを落とした女の子'
  },
  setting: {
    themeSettings: 'テーマ設定',
    themeColor: 'テーマカラー',
    systemLayout: 'システムレイアウト設定',
    enableTopNav: 'トップナビを有効化',
    enableTagsView: 'Tags-Viewを有効化',
    fixedHeader: '固定ヘッダー',
    showLogo: 'ロゴを表示',
    saveConfig: '設定を保存',
    resetConfig: '設定をリセット',
    saving: 'ローカルに保存中です。お待ちください...',
    clearing: '設定キャッシュをクリアしてリフレッシュ中です。お待ちください...'
  },
  rightToolbar: {
    showSearch: '検索を表示',
    hideSearch: '検索を非表示',
    refresh: 'リフレッシュ',
    showHideColumns: '列の表示/非表示',
    showHide: '表示/非表示',
    show: '表示',
    hide: '非表示'
  },
  navbar: {
    dashboard: 'ダッシュボード',
    github: 'プロジェクトURL',
    logOut: 'ログアウト',
    profile: '個人情報',
    theme: 'テーマ変更',
    size: 'レイアウトサイズ'
  },
  login: {
    title: 'DataLink管理システムログイン',
    username: {
      placeholder: 'ユーザー名',
      empty: 'ユーザー名は必須です'
    },
    password: {
      placeholder: 'パスワード',
      empty: 'パスワードは必須です'
    },
    verificationCode: {
      placeholder: '確認コード',
      empty: '確認コードは必須です'
    },
    rememberPwd: 'パスワードを記憶',
    logIn: 'ログイン',
    loggingIn: 'ログイン中...'
  },
  settings: {
    title: 'システムレイアウト設定',
    theme: 'テーマカラー',
    tagsView: 'Tags-Viewを有効化',
    fixedHeader: '固定ヘッダー',
    sidebarLogo: 'サイドバーのロゴ'
  },
  tagsView: {
    refresh: 'リフレッシュ',
    close: '閉じる',
    closeOthers: '他を閉じる',
    closeAll: 'すべて閉じる'
  },
  notification: {
    logout: {
      title: 'プロンプト',
      message: 'ログアウトしてシステムを終了しますか？'
    },
    ok: '確認',
    cancel: 'キャンセル'
  },
  language: {
    message: '言語切り替えに成功しました'
  },
  utils: {
    request: {
      sessionExpired:
        'ログインセッションが期限切れです。再度ログインしてください。',
      systemPrompt: 'システムプロンプト',
      relogin: '再ログイン',
      cancel: 'キャンセル',
      networkError: 'バックエンドAPI接続エラー',
      requestTimeout:
        'システムインターフェースのリクエストがタイムアウトしました',
      requestError: 'システムインターフェース {statusCode} エラー'
    }
  },
  supplyPlan: {
    plan: {
      title: '支給計画',
      tableTitle: '支給計画表',
      lastMonthRemaining: '前月支給残',
      monthlyTotal: '月合計',
      week1: '第1週目',
      week2: '第2週目',
      week3: '第3週目',
      week4: '第4週目',
      week5: '第5週目',
      week6: '第6週目',
      week7: '第7週目',
      planAmount: '計画数',
      actualAmount: '実績数',
      days: {
        mon: '月',
        tue: '火',
        wed: '水',
        thu: '木',
        fri: '金',
        sat: '土',
        sun: '日'
      }
    },
    form: {
      yearMonth: '年月',
      client: '取引先',
      clientPlaceholder: '取引先を入力してください',
      depot: 'デポ',
      depotPlaceholder: 'デポを入力してください',
      factory: '要求工場',
      factoryPlaceholder: '要求工場を入力してください',
      partNumber: '部品番号',
      partNumberPlaceholder: '部品番号を入力してください'
    },
    factories: {
      factory1: '工場1',
      factory2: '工場2',
      factory3: '工場3'
    },
    buttons: {
      search: '検索',
      reset: 'リセット'
    },
    info: {
      basicInfo: '基本情報',
      client: '取引先',
      depot: 'デポ',
      factory: '工場',
      partNumber: '部品番号',
      requirement: '必要数量',
      nMonth: '今月',
      n1Month: '来月',
      n2Month: '再来月',
      monthly: '月次計画',
      daily: '日次計画',
      deliveryMethod: '納入方式',
      supplyType: '支給区分',
      supplyMethod: '支給方式',
      deliveryLocation: '納入場所',
      basicUnit: '基本単位',
      orderUnit: '発注単位',
      allocationLot: '手配ロット',
      snep: 'SNEP',
      currentYearMonth: '年月',
      clientInventory: '取引先在庫',
      confirmedDate: '確定日',
      updatedDateTime: '更新日時'
    }
  },
  internalForecast: {
    title: '内示ダウンロードを計画します',
    buttons: {
      downloadWeekly: '週間デイリー内示ダウンロード',
      downloadThreeMonths: '3ヶ月の内示ダウンロード',
      downloadYearly: '年間内示ダウンロード',
      downloadWeeklyTxt: '週間デイリー内示のtxtファイルダウンロード',
      downloadThreeMonthsTxt: '3ヶ月の内示のtxtファイルダウンロード',
      downloadYearlyTxt: '年間内示のtxtファイルダウンロード'
    },
    badge: {
      undownloaded: '未ダウンロード'
    }
  },
  loadProposalVehicle: {
    function: '機能',
    functionSearch: '照会',
    functionStatusChange: '状態変更',
    functionShipment: '配車登録',
    status: 'ステータス',
    statusAll: '全て',
    statusDelivered: '配完',
    statusConfirmed: '配確',
    materialFactor: '料率区分',
    materialP: 'P',
    materialQ: 'Q',
    materialF: 'F',
    transportCompany: '輸送会社',
    client: '取引先',
    pickupDate: '引取日-時間',
    deliveryDate: '納入日-時間',
    depot: 'デポ',
    factory: '工場',
    deliveryLocation: '納入場所',
    ticketNo: '荷量チケット番号',
    search: '検索',
    reload: '再表示',
    clear: 'クリア',
    addRow: '行を追加',
    deleteSelected: '選択したものを削除',
    to: '-',
    startDate: '開始日',
    endDate: '終了日',
    addRowWarning: 'データを保存するには、確認ボタンをクリックしてください！',
    placeholder: {
      depot: '倉庫を入力してください',
      factory: '工場を入力してください',
      deliveryLocation: '配達場所を入力してください',
      ticketNo: '貨物伝票番号を入力してください',
      materialFactorPlaceholder: '料率区分を入力してください'
    }
  },
  loadProposalVehicleTable: {
    transportPlanId: '番号',
    status: 'ステータス',
    statusDraft: 'ドラフト',
    statusConfirmed: '荷確',
    statusInTransit: '配中',
    statusAssigned: '配確',
    statusCompleted: '配完',
    statusDelivered: '納完',
    materialFactor: '料率区分',
    way: 'way',
    pickupDateTime: '引取日時',
    pickupDate: '年月日',
    pickupTime: '時分',
    clientCode: '取引先',
    depot: 'デポ',
    deliveryLocation: '納入場所',
    factory: '工場',
    deliveryDateTime: '納入日時',
    deliveryDate: '年月日',
    deliveryTime: '時分',
    port: '出荷Port',
    weight: '荷量',
    weightTotal: '山数',
    palletQuantity: 'パレット数',
    totalQuantity: '総数',
    average: '平数',
    ticketNo: '荷量チケットNo',
    transportPlan: '配車計画',
    company: '輸送会社',
    pickupTimeShort: '集荷時分',
    pickupQuantity: '集荷山数',
    carType: '車格',
    carNo: '車両No',
    driver: '運転手',
    deliveryLocationName: '納入先名',
    companyName: '輸送会社名',
    recordMaintenance: 'レコードメンテナンス情報',
    registrationInfo: '登録情報',
    createDate: '登録年月日',
    createTime: '登録時分',
    createBy: '登録ユーザーID',
    updateInfo: '情報更新',
    updateDate: '更新年月日',
    updateTime: '更新時分',
    updateBy: '更新ユーザーID',
    actions: '操作',
    confirm: '確認',
    smallCar: '小型車',
    mediumCar: '中型車',
    largeCar: '大型車',
    placeholder: {
      depot: '倉庫を入力してください',
      factory: '工場を入力してください',
      deliveryLocation: '配達場所を入力してください',
      ticketNo: '貨物伝票番号を入力してください',
      materialFactorPlaceholder: '料率区分を入力してください'
    }
  },
  kanban: {
    form: {
      demandCode: '要元',
      issueNo: '発行ＮＯ',
      deliveryInstructionYy: '納入指示日時分秒',
      deliveryInstructionDate: '納入指示日年月日',
      deliveryInstructionTime: '納入指示日時分',
      maker: 'メーカー',
      depot: 'デポ',
      customerPartsNo: '部品番号',
      deliveryTicketNo: '納品チケットＮＯ',
      search: '検索',
      reset: 'リセット'
    },
    placeholder: {
      demandCode: '要元を入力してください',
      issueNo: '発行ＮＯを入力してください',
      deliveryInstructionDate: '納入指示日年月日を入力してください',
      deliveryInstructionTime: '納入指示日時分を入力してください',
      deliveryInstructionYy: '納入指示日時分秒を入力してください',
      maker: 'メーカーを入力してください',
      depot: 'デポを入力してください',
      customerPartsNo: '部品番号を入力してください',
      deliveryTicketNo: '納品チケットＮＯを入力してください'
    },
    button: {
      search: '検索',
      reset: 'リセット',
      printSvSpec: '納入指示(SV諸元付)',
      printLineKdSpec: '納入指示(ラインKD諸元付)',
      printLineSpec: '納入指示(Line諸元付)'
    },
    table: {
      demandCode: '要元',
      issueNo: '発行ＮＯ',
      deliveryInstructionDate: '納入指示日年月日',
      deliveryInstructionTime: '納入指示日時分',
      deliveryInstructionYy: '納入指示日時分秒',
      maker: 'メーカー',
      depot: 'デポ',
      customerPartsNo: '部品番号',
      deliveryTicketNo: '納品チケットＮＯ'
    },
    alert: {
      selectKanban: '少なくとも1件のかんばんを選択してください',
      printSuccess: '印刷に成功しました',
      printError: '印刷に失敗しました'
    }
  }
}
