<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item :label="$t('asn.asnCode')" prop="asnCode">
        <el-input
          v-model="queryParams.asnCode"
          :placeholder="$t('asn.enterAsnCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('asn.compCode')" prop="compCode">
        <el-input
          v-model="queryParams.compCode"
          :placeholder="$t('asn.enterCompCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('asn.suppCode')" prop="suppCode">
        <el-input
          v-model="queryParams.suppCode"
          :placeholder="$t('asn.enterSuppCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('asn.suppName')" prop="suppName">
        <el-input
          v-model="queryParams.suppName"
          :placeholder="$t('asn.enterSuppName')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('asn.status')" prop="kafkaStatus">
        <el-select v-model="queryParams.kafkaStatus" :placeholder="$t('asn.status')" clearable size="small">
          <el-option
            v-for="dict in statusOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item :label="$t('asn.planDeliveryDate')">
        <el-date-picker v-model="daterangePlanDeliveryDate" size="small"  value-format="yyyy-MM-dd"
          type="daterange" range-separator="-" :start-placeholder="$t('asn.startDate')"
          :end-placeholder="$t('asn.endDate')"></el-date-picker>
      </el-form-item> -->
      <el-form-item :label="$t('asn.deliveryDate')">
        <el-date-picker
          v-model="daterangeDeliveryDate"
          size="small"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('asn.startDate')"
          :end-placeholder="$t('asn.endDate')"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t('asn.search') }}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t('asn.reset')
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          :loading="printPickListLoading"
          @click="printPickList"
        >
          {{ $t('asn.button.printPickList') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-printer"
          size="mini"
          :loading="printNpsSlsLoading"
          @click="printNpsSls"
        >
          {{ $t('asn.button.printNpsSls') }}
        </el-button>
        <!--  <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-printer"
            size="mini"
            :loading="printPalletTagLoading"
            @click="printPalletTag"
          >
            {{ $t('asn.button.printPalletTag') }}
          </el-button>
        </el-col> -->
      </el-col>

    </el-row>
    <el-table v-loading="loading" :data="asnList" @row-click="handleRowClick">
      <el-table-column width="50">
        <template slot-scope="{row}">
          <el-radio v-model="checkASNId" inert :label="row.asnId" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('asn.asnCode')" align="center" prop="asnCode">
        <template slot-scope="scope">
          <el-button v-hasPermi="['datamanage:asn:edit']" type="text" @click="gotoDetail(scope.row)">{{
            scope.row.asnCode }}</el-button>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('asn.compCode')"
        align="center"
        prop="compCode"
      />
      <el-table-column
        :label="$t('asn.suppCode')"
        align="center"
        prop="suppCode"
      />
      <el-table-column
        :label="$t('asn.suppName')"
        align="center"
        prop="suppName"
      />
      <!-- <el-table-column :label="$t('asn.planDeliveryDate')" align="center" prop="planDeliveryDate" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.planDeliveryDate }}</span>
        </template>
      </el-table-column> -->
      <el-table-column :label="$t('asn.deliveryDate')" align="center" prop="deliveryDate" width="180">
        <template slot-scope="scope">
          <span>{{ dayjs(scope.row.deliveryDate).format('YYYY-MM-DD HH:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('asn.status')"
        align="center"
        prop="kafkaStatus"
        :formatter="typeFormat"
      />
      <el-table-column :label="$t('asn.actionsText')" align="center" class-name="small-padding fixed-width">

        <template slot-scope="scope">
          <el-button
            v-if="scope.row.kafkaStatus === '0'"
            v-hasPermi="['datamanage:asn:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row.asnId)"
          >{{ $t('asn.editText') }}</el-button>

          <el-button
            v-if="
              scope.row.kafkaStatus === '0'
            "
            v-hasPermi="['datamanage:asn:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >{{ $t('asn.delete') }}</el-button>
        </template>

      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>
<script>
import {
  addAsn,
  delAsn,
  exportAsn,
  listAsn,
  updateAsn,
  printNpsSls,
  printPickingListByAsnCode
} from '@/api/datamanage/asn'
import { getToken } from '@/utils/auth'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { mapActions } from 'vuex'
import timezone from 'dayjs/plugin/timezone'

dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(utc)

export default {
  name: 'Asn',
  data() {
    return {
      // 遮罩层
      loading: true,
      submitLoading: false,
      printPickListLoading: false,
      printPalletTagLoading: false,
      printNpsSlsLoading: false,
      // 单选
      checkASNId: null,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // ASN表格数据
      asnList: [],
      statusOptions: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      daterangePlanDeliveryDate: [],
      daterangeDeliveryDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        asnCode: null,
        docno: null,
        compCode: null,
        suppCode: null,
        suppName: null,
        docdate: null,
        dnno: null,
        planDeliveryDate: null,
        deliveryDate: null,
        orderByColumn: 'createTime',
        isAsc: 'desc'
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        asncode: [
          {
            required: true,
            message: this.$t('asn.validation.asnCodeRequired'),
            trigger: 'blur'
          }
        ],
        docno: [
          {
            required: true,
            message: this.$t('asn.validation.docnoRequired'),
            trigger: 'blur'
          }
        ],
        compcode: [
          {
            required: true,
            message: this.$t('asn.validation.compCodeRequired'),
            trigger: 'blur'
          }
        ],
        suppcode: [
          {
            required: true,
            message: this.$t('asn.validation.suppCodeRequired'),
            trigger: 'blur'
          }
        ],
        suppname: [
          {
            required: true,
            message: this.$t('asn.validation.suppNameRequired'),
            trigger: 'blur'
          }
        ],
        docdate: [
          {
            required: true,
            message: this.$t('asn.validation.docDateRequired'),
            trigger: 'blur'
          }
        ],
        dnno: [
          {
            required: true,
            message: this.$t('asn.validation.dnnoRequired'),
            trigger: 'blur'
          }
        ],
        plandeliverydate: [
          {
            required: true,
            message: this.$t('asn.validation.planDeliveryDateRequired'),
            trigger: 'blur'
          }
        ],
        deliverydate: [
          {
            required: true,
            message: this.$t('asn.validation.deliveryDateRequired'),
            trigger: 'blur'
          }
        ]
      },
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: this.$t('asn.upload.title'),
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/datamanage/asn/importData'
      }
    }
  },
  computed: {
    dayjs() {
      return dayjs
    }
  },
  created() {
    // if (this.$route.path.toLowerCase().endsWith('recv/asn')) {
    //   this.queryParams.direction = 'I'
    // } else {
    //   this.queryParams.direction = 'O'
    // }
    this.getList()
    this.getDicts('data_status').then(res => {
      this.statusOptions = res.data
    })
  },
  activated() {
    this.getList()
  },
  methods: {
    ...mapActions('asn', ['setAsnEdit', 'clearAsnData']),

    /** 查询ASN列表 */
    getList() {
      this.loading = true
      this.queryParams.params = {}
      // if (
      //   null != this.daterangePlanDeliveryDate &&
      //   '' != this.daterangePlanDeliveryDate
      // ) {
      //   this.queryParams.params['beginPlanDeliveryDate'] =
      //     this.daterangePlanDeliveryDate[0]
      //   this.queryParams.params['endPlanDeliveryDate'] =
      //     this.daterangePlanDeliveryDate[1]
      // }
      if (
        null != this.daterangeDeliveryDate &&
        '' != this.daterangeDeliveryDate
      ) {
        this.queryParams.params['beginDeliveryDate'] =
          dayjs(this.daterangeDeliveryDate[0]).utc().format('YYYY-MM-DD HH:mm:ss')
        this.queryParams.params['endDeliveryDate'] =
          dayjs(this.daterangeDeliveryDate[1]).utc().format('YYYY-MM-DD HH:mm:ss')
      }
      listAsn(this.queryParams).then(response => {
        this.asnList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        asnid: null,
        asncode: null,
        docno: null,
        compcode: null,
        suppcode: null,
        suppname: null,
        docdate: null,
        dnno: null,
        plandeliverydate: null,
        deliverydate: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangePlanDeliveryDate = []
      this.daterangeDeliveryDate = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    handleRowClick(row) {
      this.checkASNId = row.asnId
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.$t('asn.actions.add')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.clearAsnData()
      this.setAsnEdit(row)
      this.$router.push({ name: 'AsnEdit' })
    },
    gotoDetail(row) {
      // if (this.queryParams.direction === 'O' && row.kafkaStatus === '0') {
      //   this.handleUpdate(row)
      // } else {
      //   this.$router.push({ name: 'AsnDetail', params: { asnId: row.asnId } })
      // }

      this.$router.push({ name: 'AsnDetail', params: { asnId: row.asnId }})
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.submitLoading = true
          if (this.form.asnid != null) {
            updateAsn(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(this.$t('asn.actions.editSuccess'))
                this.open = false
                this.getList()
              }
            }).finally(() => {
              this.submitLoading = false
            })
          } else {
            addAsn(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(this.$t('asn.actions.addSuccess'))
                this.open = false
                this.getList()
              }
            }).finally(() => {
              this.submitLoading = false
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const asnids = row.asnId
      this.$confirm(
        this.$t('asn.actions.confirmDelete', { asnCode: row.asnCode }),
        this.$t('asn.actions.warningTitle'),
        {
          confirmButtonText: this.$t('asn.actions.confirm'),
          cancelButtonText: this.$t('asn.actions.cancel'),
          type: 'warning'
        }
      )
        .then(function () {
          return delAsn(asnids)
        })
        .then(() => {
          this.getList()
          this.msgSuccess(this.$t('asn.actions.deleteSuccess'))
        })
        .catch(function () { })
    },
    /** 打印现品票 */
    printPalletTag() {
      this.printNpsSlsLoading = true
    },
    /** 打印纳品📖-受领书 */
    printNpsSls() {
      if (this.checkASNId === null) {
        this.$alert(this.$t('asn.alert.selectOnePrint'))
        return
      }
      this.printNpsSlsLoading = true
      const loading = this.$loading({
        lock: false,
        fullscreen: true,
        text: this.$t('system.common.loading'),
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 设置超时15秒后自动关闭loading
      const timeout = setTimeout(() => {
        loading.close()
      }, 15000)
      printNpsSls(this.checkASNId, dayjs.tz.guess())
        .then((response) => {
          loading.close()
          window.open(
            process.env.VUE_APP_BASE_API +
              '/common/downloadPdf?fileName=' +
              encodeURI(response.msg) +
              '&delete=false'
          )
          this.printNpsSlsLoading = false
        })
        .catch(() => {
          loading.close()
          this.printNpsSlsLoading = false
        })
    },
    /** 打印提货单 */
    printPickList() {
      if (this.checkASNId === null) {
        this.$alert(this.$t('asn.alert.selectOnePrint'))
        return
      }
      this.printPickingListLoading = true
      const loading = this.$loading({
        lock: false,
        fullscreen: true,
        text: this.$t('system.common.loading'),
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 设置超时15秒后自动关闭loading
      const timeout = setTimeout(() => {
        loading.close()
      }, 15000)
      printPickingListByAsnCode(this.checkASNId, dayjs.tz.guess())
        .then((response) => {
          loading.close()
          window.open(
            process.env.VUE_APP_BASE_API +
              '/common/downloadPdf?fileName=' +
              encodeURI(response.msg) +
              '&delete=false'
          )
          this.printPickingListLoading = false
        })
        .catch(() => {
          loading.close()
          this.printPickingListLoading = false
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams
      this.$confirm(
        this.$t('asn.actions.confirmExport'),
        this.$t('asn.actions.warningTitle'),
        {
          confirmButtonText: this.$t('asn.actions.confirm'),
          cancelButtonText: this.$t('asn.actions.cancel'),
          type: 'warning'
        }
      )
        .then(function () {
          return exportAsn(queryParams)
        })
        .then(response => {
          this.download(response.msg)
        })
        .catch(function () { })
    },

    handleImport() {
      this.upload.title = this.$t('asn.upload.title')
      this.upload.open = true
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      this.$alert(response.msg, this.$t('asn.upload.resultTitle'), {
        dangerouslyUseHTMLString: true
      })
      this.getList()
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit()
    },
    typeFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.kafkaStatus)
    }
  }
}
</script>

<style scoped>
.el-table {
  .el-radio {
    .el-radio__label {
      display: none;
    }
  }
}

</style>
